#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K230正确版本 - 基于官方例程的UART配置
"""

import time
from machine import UART
from machine import FPIOA

class K230Correct:
    def __init__(self):
        """初始化K230程序 - 使用正确的UART配置"""
        print("=== K230正确版本启动 ===")
        
        try:
            # 配置引脚 - 基于例程
            self.fpioa = FPIOA()
            self.fpioa.set_function(11, FPIOA.UART2_TXD)  # 引脚11作为TX
            self.fpioa.set_function(12, FPIOA.UART2_RXD)  # 引脚12作为RX
            print("✓ FPIOA引脚配置完成")
            
            # 初始化UART2 - 使用完整参数
            self.uart = UART(UART.UART2, 
                           baudrate=115200, 
                           bits=UART.EIGHTBITS, 
                           parity=UART.PARITY_NONE, 
                           stop=UART.STOPBITS_ONE)
            print("✓ UART2初始化成功 (115200波特率)")
            print(f"✓ UART对象: {self.uart}")
            
        except Exception as e:
            print(f"✗ UART初始化失败: {e}")
            self.uart = None
            self.fpioa = None
        
        # 计数器
        self.photo_count = 0
        self.test_count = 0
        self.receive_count = 0
        
        print("✓ K230正确版本初始化完成")
        self.send_message("CORRECT_INIT_OK")
    
    def send_message(self, msg):
        """发送消息到UART"""
        if not self.uart:
            print(f"[无UART] 应发送: {msg}")
            return
        
        try:
            data = f"{msg}\r\n".encode('utf-8')
            self.uart.write(data)
            print(f"✓ 发送: {msg}")
        except Exception as e:
            print(f"✗ 发送失败: {e}")
    
    def read_uart_correct(self):
        """正确的UART读取方法 - 基于例程"""
        if not self.uart:
            return None
        
        try:
            # 使用例程中的方法：无参数read()
            data = self.uart.read()
            
            if data and len(data) > 0:
                print(f"✓ 读取到: {data} (长度: {len(data)}, 类型: {type(data)})")
                return data
            
            return None
            
        except Exception as e:
            print(f"✗ 读取异常: {e}")
            return None
    
    def process_command(self, cmd):
        """处理命令"""
        cmd = cmd.strip().lower()
        print(f"✓ 执行命令: '{cmd}'")
        
        if cmd == "photo":
            self.photo_count += 1
            print(f"✓ 照片命令 (总数: {self.photo_count})")
            self.send_message(f"PHOTO_OK:{self.photo_count}")
            
        elif cmd == "test":
            self.test_count += 1
            print(f"✓ 测试命令 (总数: {self.test_count})")
            self.send_message(f"TEST_OK:{self.test_count}")
            
        elif cmd == "ping":
            print("✓ Ping命令")
            self.send_message("PONG")
            
        elif cmd == "count":
            self.send_message(f"COUNT:{self.photo_count}")
            
        elif cmd == "status":
            self.send_message(f"STATUS:P{self.photo_count}_T{self.test_count}_R{self.receive_count}")
            
        elif cmd == "reset":
            self.photo_count = 0
            self.test_count = 0
            self.receive_count = 0
            self.send_message("RESET_OK")
            
        elif cmd == "info":
            self.send_message(f"INFO:K230_CORRECT_P{self.photo_count}")
            
        else:
            print(f"✗ 未知命令: {cmd}")
            self.send_message(f"UNKNOWN:{cmd}")
    
    def main_loop(self):
        """主循环"""
        print("✓ K230正确版本开始运行")
        self.send_message("CORRECT_READY")
        
        buffer = ""
        loop_count = 0
        last_heartbeat = 0
        
        while True:
            try:
                # 读取数据
                data = self.read_uart_correct()
                
                if data:
                    self.receive_count += 1
                    print(f"✓ 第{self.receive_count}次接收数据")
                    
                    # 处理数据
                    if isinstance(data, bytes):
                        text = data.decode('utf-8', errors='ignore')
                    else:
                        text = str(data)
                    
                    buffer += text
                    print(f"✓ 当前缓冲区: '{buffer}'")
                    
                    # 处理完整命令
                    while '\n' in buffer or '\r' in buffer:
                        if '\n' in buffer:
                            line, buffer = buffer.split('\n', 1)
                        else:
                            line, buffer = buffer.split('\r', 1)
                        
                        command = line.strip('\r\n ')
                        if command:
                            print(f"✓ 提取到命令: '{command}'")
                            self.process_command(command)
                
                # 状态显示
                loop_count += 1
                current_time = time.ticks_ms()
                
                # 每5秒显示状态
                if loop_count % 50 == 0:  # 每50次循环
                    print(f"→ 运行状态: 循环{loop_count}, 接收{self.receive_count}次, 照片{self.photo_count}, 测试{self.test_count}")
                
                # 每15秒心跳
                if current_time - last_heartbeat > 15000:
                    print(f"💓 心跳发送")
                    self.send_message("HEARTBEAT")
                    last_heartbeat = current_time
                
                time.sleep_ms(100)  # 100ms延时
                
            except KeyboardInterrupt:
                print("\n程序被中断")
                break
            except Exception as e:
                print(f"✗ 主循环错误: {e}")
                time.sleep_ms(200)
        
        # 清理资源
        try:
            if self.uart:
                self.uart.deinit()
                print("✓ UART资源已释放")
        except:
            pass
        
        print("✓ K230正确版本结束")

def main():
    """主函数"""
    print("=" * 60)
    print("    K230 正确版本 (基于官方例程)")
    print("=" * 60)
    print("硬件连接:")
    print("  - K230引脚11 (TX) → WCH-Link RX")
    print("  - K230引脚12 (RX) ← WCH-Link TX")
    print("  - 波特率: 115200")
    print("=" * 60)
    print("支持命令:")
    print("  photo, test, ping, count, status, reset, info")
    print("=" * 60)
    
    try:
        app = K230Correct()
        app.main_loop()
    except Exception as e:
        print(f"程序异常: {e}")
        try:
            import sys
            sys.print_exception(e)
        except:
            pass

if __name__ == "__main__":
    main()
