#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K230 Basic Test - Absolute Minimal Version
"""

import time

def basic_test():
    """Most basic test without any imports that might fail"""
    print("=== K230 Basic Test ===")
    
    # Test 1: Basic Python functionality
    try:
        count = 0
        for i in range(3):
            count += 1
            print(f"Count: {count}")
            time.sleep_ms(500)
        print("✓ Basic loop test passed")
    except Exception as e:
        print(f"✗ Basic test failed: {e}")
        return
    
    # Test 2: File operations
    try:
        filename = "basic_test.txt"
        with open(filename, 'w') as f:
            f.write("Basic test file\n")
            f.write(f"Timestamp: {time.ticks_ms()}\n")
        print(f"✓ File test passed: {filename}")
    except Exception as e:
        print(f"✗ File test failed: {e}")
        return
    
    # Test 3: Try UART import
    try:
        from machine import UART
        print("✓ UART import successful")
        
        # Test 4: Try simple UART creation
        try:
            uart = UART(1, 115200)
            print("✓ UART creation successful")
            
            # Test 5: Try UART write
            try:
                uart.write(b"HELLO\r\n")
                print("✓ UART write successful")
                
                # Test 6: Simple communication loop
                print("Starting communication test...")
                for i in range(10):
                    try:
                        uart.write(f"TEST_{i}\r\n".encode())
                        print(f"Sent: TEST_{i}")
                        
                        if uart.any():
                            data = uart.read()
                            print(f"Received: {data}")
                        
                        time.sleep_ms(1000)
                        
                    except Exception as e:
                        print(f"Loop {i} error: {e}")
                        break
                
                print("✓ Communication test completed")
                
            except Exception as e:
                print(f"✗ UART write failed: {e}")
        
        except Exception as e:
            print(f"✗ UART creation failed: {e}")
    
    except Exception as e:
        print(f"✗ UART import failed: {e}")
    
    print("=== Test completed ===")

if __name__ == "__main__":
    try:
        basic_test()
    except Exception as e:
        print(f"Program exception: {e}")
        # Try to get more error details
        try:
            import sys
            sys.print_exception(e)
        except:
            print("Cannot print exception details")
