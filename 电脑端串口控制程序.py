#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电脑端串口控制程序
用于控制CH32V307单片机，实现多种功能
支持双串口通信：
- 串口1: 调试和基本命令 (UART1)
- 串口2: 控制命令 (UART3)
"""

import serial
import time
import threading
import sys

class MCUController:
    def __init__(self):
        self.debug_port = None      # UART1 - 调试串口
        self.control_port = None    # UART3 - 控制串口
        self.running = False
        
    def connect_ports(self, debug_com, control_com, baudrate=115200):
        """连接串口"""
        try:
            # 连接调试串口 (UART1)
            self.debug_port = serial.Serial(
                port=debug_com,
                baudrate=baudrate,
                bytesize=8,
                parity='N',
                stopbits=1,
                timeout=1
            )
            print(f"? 调试串口连接成功: {debug_com}")
            
            # 连接控制串口 (UART3)
            self.control_port = serial.Serial(
                port=control_com,
                baudrate=baudrate,
                bytesize=8,
                parity='N',
                stopbits=1,
                timeout=1
            )
            print(f"? 控制串口连接成功: {control_com}")
            
            self.running = True
            return True
            
        except Exception as e:
            print(f"? 串口连接失败: {e}")
            return False
    
    def disconnect_ports(self):
        """断开串口连接"""
        self.running = False
        
        if self.debug_port and self.debug_port.is_open:
            self.debug_port.close()
            print("调试串口已断开")
            
        if self.control_port and self.control_port.is_open:
            self.control_port.close()
            print("控制串口已断开")
    
    def send_debug_command(self, command):
        """发送调试命令到UART1"""
        if self.debug_port and self.debug_port.is_open:
            try:
                cmd = command.encode('utf-8') + b'\r\n'
                self.debug_port.write(cmd)
                print(f"? 调试命令已发送: {command}")
                return True
            except Exception as e:
                print(f"? 调试命令发送失败: {e}")
                return False
        else:
            print("? 调试串口未连接")
            return False
    
    def send_control_command(self, command):
        """发送控制命令到UART3"""
        if self.control_port and self.control_port.is_open:
            try:
                cmd = command.encode('utf-8') + b'\r\n'
                self.control_port.write(cmd)
                print(f"? 控制命令已发送: {command}")
                return True
            except Exception as e:
                print(f"? 控制命令发送失败: {e}")
                return False
        else:
            print("? 控制串口未连接")
            return False
    
    def read_debug_response(self):
        """读取调试串口响应"""
        while self.running:
            try:
                if self.debug_port and self.debug_port.is_open and self.debug_port.in_waiting:
                    data = self.debug_port.readline().decode('utf-8', errors='ignore').strip()
                    if data:
                        print(f"? 调试响应: {data}")
                time.sleep(0.1)
            except Exception as e:
                print(f"? 读取调试响应失败: {e}")
                break
    
    def read_control_response(self):
        """读取控制串口响应"""
        while self.running:
            try:
                if self.control_port and self.control_port.is_open and self.control_port.in_waiting:
                    data = self.control_port.readline().decode('utf-8', errors='ignore').strip()
                    if data:
                        print(f"? 控制响应: {data}")
                time.sleep(0.1)
            except Exception as e:
                print(f"? 读取控制响应失败: {e}")
                break
    
    def start_monitoring(self):
        """开始监控串口响应"""
        if self.running:
            # 启动调试串口监控线程
            debug_thread = threading.Thread(target=self.read_debug_response, daemon=True)
            debug_thread.start()
            
            # 启动控制串口监控线程
            control_thread = threading.Thread(target=self.read_control_response, daemon=True)
            control_thread.start()
            
            print("? 串口监控已启动")

def show_menu():
    """显示菜单"""
    print("\n" + "="*50)
    print("CH32V307 串口控制程序")
    print("="*50)
    print("调试命令 (UART1):")
    print("  1. photo     - 拍照")
    print("  2. status    - 状态查询")
    print("  3. test      - 测试K230")
    print("  4. help      - 帮助信息")
    print("  5. send:数据 - 发送自定义数据")
    print()
    print("控制命令 (UART3):")
    print("  6. K230:命令  - 转发命令给K230")
    print("  7. LED_ON    - 开启LED")
    print("  8. LED_OFF   - 关闭LED")
    print("  9. RESET     - 系统重置")
    print()
    print("系统命令:")
    print("  0. 退出程序")
    print("  m. 显示菜单")
    print("="*50)

def main():
    """主程序"""
    print("CH32V307 双串口控制程序启动...")
    
    # 创建控制器实例
    controller = MCUController()
    
    # 配置串口
    print("\n请配置串口连接:")
    debug_com = input("请输入调试串口号 (如 COM3): ").strip()
    control_com = input("请输入控制串口号 (如 COM4): ").strip()
    
    if not debug_com or not control_com:
        print("? 串口号不能为空")
        return
    
    # 连接串口
    if not controller.connect_ports(debug_com, control_com):
        return
    
    # 启动监控
    controller.start_monitoring()
    
    # 显示菜单
    show_menu()
    
    try:
        while True:
            command = input("\n请输入命令 (输入m显示菜单): ").strip()
            
            if command == '0':
                print("正在退出程序...")
                break
            elif command == 'm':
                show_menu()
            elif command == '1':
                controller.send_debug_command("photo")
            elif command == '2':
                controller.send_debug_command("status")
            elif command == '3':
                controller.send_debug_command("test")
            elif command == '4':
                controller.send_debug_command("help")
            elif command.startswith('5 '):
                data = command[2:]
                controller.send_debug_command(f"send:{data}")
            elif command.startswith('6 '):
                k230_cmd = command[2:]
                controller.send_control_command(f"K230:{k230_cmd}")
            elif command == '7':
                controller.send_control_command("LED_ON")
            elif command == '8':
                controller.send_control_command("LED_OFF")
            elif command == '9':
                controller.send_control_command("RESET")
            elif command.startswith('debug:'):
                # 直接发送调试命令
                cmd = command[6:]
                controller.send_debug_command(cmd)
            elif command.startswith('ctrl:'):
                # 直接发送控制命令
                cmd = command[5:]
                controller.send_control_command(cmd)
            else:
                print("? 未知命令，输入m查看菜单")
    
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    
    finally:
        controller.disconnect_ports()
        print("程序已退出")

if __name__ == "__main__":
    # 检查依赖
    try:
        import serial
    except ImportError:
        print("? 缺少pyserial库，请安装:")
        print("pip install pyserial")
        sys.exit(1)
    
    main()
