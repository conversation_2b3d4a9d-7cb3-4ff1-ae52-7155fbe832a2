/*
 * CH32V307VCT6 Main Program - UTF-8 Version
 * Multi-UART Communication System
 * Supports PC control, K230 communication, and device management
 */

/* Header files */
#include "debug.h"      // Debug utilities
#include "timer.h"      // Timer functions
#include "uart.h"       // UART communication
#include "string.h"     // String operations
#include "esp8266.h"    // WiFi module
#include "config.h"     // Configuration
#include "k230_camera.h"        // K230 camera control
#include "display_driver.h"     // Display driver
#include "tcm_diagnosis.h"      // TCM diagnosis

/* Interrupt function declarations */
void TIM3_IRQHandler(void) __attribute__((interrupt("WCH-Interrupt-fast")));    // Timer3 interrupt
void USART1_IRQHandler(void) __attribute__((interrupt("WCH-Interrupt-fast")));  // UART1 debug interrupt
void USART3_IRQHandler(void) __attribute__((interrupt("WCH-Interrupt-fast")));  // UART3 control interrupt
void UART6_IRQHandler(void) __attribute__((interrupt("WCH-Interrupt-fast")));   // UART6 K230 interrupt
void UART8_IRQHandler(void) __attribute__((interrupt("WCH-Interrupt-fast")));   // UART8 ESP8266 interrupt
void USART2_IRQHandler(void) __attribute__((interrupt("WCH-Interrupt-fast")));  // UART2 RaspberryPi interrupt

/* Global variables */
unsigned long int uwtick;             // System tick counter

// UART1 receive variables (PC debug communication) - reduced buffer size
u8 uart1_rec_string[64] = {0};
u8 uart1_rec_index = 0;
u8 uart1_rec_tick = 0;

// UART3 receive variables (PC control communication)
u8 uart3_rec_string[128] = {0};
u8 uart3_rec_index = 0;
u8 uart3_rec_tick = 0;

// UART6 receive variables (K230 communication) - reduced buffer size
u8 uart6_rec_string[128] = {0};
u8 uart6_rec_tick;
u8 uart6_rec_index;

// UART8 receive variables (ESP8266 communication) - reduced buffer size
u8 uart8_rec_string[128] = {0};
u8 uart8_rec_index = 0;
u8 uart8_rec_tick = 0;

// UART2 receive variables (RaspberryPi communication) - reduced buffer size
u8 uart2_rec_string[256] = {0};
u8 uart2_rec_index = 0;
u8 uart2_rec_tick = 0;

// System flags
u8 tcm_start_flag = 0;

/* Function prototypes */
void esp8266_proc(void);
void uart8_proc(void);
void uart6_proc(void);
void uart2_proc(void);
void usart1_proc(void);
void uart3_proc(void);
void tcm_main_task(void);

/* Task scheduler */
// #include "scheduler.h"  // Comment out if scheduler.h not available

// Simple task structure definition
typedef struct {
    void (*task_func)(void);    // Task function pointer
    u16 period;                 // Task period in ms
    u16 counter;                // Task counter
} task_t;

// Simple scheduler functions
void scheduler_init(void) {
    // Initialize scheduler
}

void scheduler_run(void) {
    // Simple task execution without complex scheduling
    static u32 last_tick = 0;
    if(uwtick - last_tick >= 1) {  // 1ms execution
        last_tick = uwtick;

        // Execute tasks directly
        if(uwtick % 1 == 0) {
            usart1_proc();
            uart3_proc();
            uart6_proc();
            uart8_proc();
        }
        if(uwtick % 2 == 0) {
            esp8266_proc();
        }
        if(uwtick % 5 == 0) {
            uart2_proc();
        }
        if(uwtick % 10 == 0) {
            tcm_main_task();
        }
    }
}

// ESP8266 WiFi module processing
void esp8266_proc()
{
    // Process ESP8266 WiFi communication
    // Handle AT commands and network data
}

// UART8 ESP8266 communication processing
void uart8_proc()
{
    if(uart8_rec_index == 0) return;
    
    if(uart8_rec_tick > 10) {
        // Process ESP8266 responses
        // Handle WiFi status and data transmission
        
        // Clear buffer
        uart8_rec_index = 0;
        memset(uart8_rec_string, 0, sizeof(uart8_rec_string));
    }
}

// K230 data processing (UART6)
void uart6_proc()
{
    if(uart6_rec_index == 0) return;
    
    if(uart6_rec_tick > 10) {
        // Check K230 responses
        if(strstr((char*)uart6_rec_string, "PHOTO_OK") != NULL) {
            printf("K230 photo capture successful\r\n");
        } else if(strstr((char*)uart6_rec_string, "PHOTO_FAIL") != NULL) {
            printf("K230 photo capture failed\r\n");
        } else if(strstr((char*)uart6_rec_string, "PHOTO_ERROR") != NULL) {
            printf("K230 photo capture error\r\n");
        }
        
        // Clear buffer
        uart6_rec_index = 0;
        memset(uart6_rec_string, 0, sizeof(uart6_rec_string));
    }
}

// RaspberryPi communication processing
void uart2_proc()
{
    if(uart2_rec_index == 0) return;

    if(uart2_rec_tick > 20) {
        // Process RaspberryPi diagnostic result data
        // Parse and handle JSON format data
        uart2_rec_index = 0;
        memset(uart2_rec_string, 0, sizeof(uart2_rec_string));
    }
}

// PC control communication processing (UART3)
void uart3_proc()
{
    if(uart3_rec_index == 0) return;
    
    if(uart3_rec_tick > 10) {
        printf("UART3 received control command: %s\r\n", uart3_rec_string);
        
        // Process control commands
        if(strstr((char*)uart3_rec_string, "K230:") != NULL) {
            // Forward to K230
            char* cmd_start = strstr((char*)uart3_rec_string, "K230:") + 5;
            printf("Forward to K230: %s\r\n", cmd_start);
            uart6_send_string((u8*)cmd_start, strlen(cmd_start));
        }
        else if(strstr((char*)uart3_rec_string, "LED_ON") != NULL) {
            printf("LED control: ON\r\n");
            // Add LED control code here
        }
        else if(strstr((char*)uart3_rec_string, "LED_OFF") != NULL) {
            printf("LED control: OFF\r\n");
            // Add LED control code here
        }
        else if(strstr((char*)uart3_rec_string, "RESET") != NULL) {
            printf("System reset command\r\n");
            // Add system reset code here
        }
        else {
            printf("UART3 unknown command: %s\r\n", uart3_rec_string);
        }
        
        // Clear buffer
        uart3_rec_index = 0;
        memset(uart3_rec_string, 0, sizeof(uart3_rec_string));
    }
}

// PC debug communication processing (UART1)
void usart1_proc()
{
    if(uart1_rec_index == 0) return;
    
    if(uart1_rec_tick > 10) {
        // Check various commands
        if(strstr((char*)uart1_rec_string, "photo") != NULL) {
            printf("Photo command received, sending to K230\r\n");
            u8 photo_cmd[] = "CAPTURE";
            uart6_send_string(photo_cmd, sizeof(photo_cmd)-1);
            printf("Photo command sent to K230 via UART6\r\n");
        }
        else if(strstr((char*)uart1_rec_string, "status") != NULL) {
            printf("System status query\r\n");
            printf("UART1: PC communication normal\r\n");
            printf("UART6: K230 communication normal\r\n");
            printf("UART3: Control communication normal\r\n");
        }
        else if(strstr((char*)uart1_rec_string, "test") != NULL) {
            printf("System test command\r\n");
            u8 test_cmd[] = "TEST_K230";
            uart6_send_string(test_cmd, sizeof(test_cmd)-1);
        }
        else if(strstr((char*)uart1_rec_string, "help") != NULL) {
            printf("Available commands:\r\n");
            printf("photo - Take photo\r\n");
            printf("status - Status query\r\n");
            printf("test - Test K230\r\n");
            printf("send:data - Send custom data to K230\r\n");
        }
        else if(strstr((char*)uart1_rec_string, "send:") != NULL) {
            char* data_start = strstr((char*)uart1_rec_string, "send:") + 5;
            printf("Send custom data to K230: %s\r\n", data_start);
            uart6_send_string((u8*)data_start, strlen(data_start));
        }
        else {
            printf("Unknown command: %s\r\n", uart1_rec_string);
            printf("Type 'help' to see available commands\r\n");
        }
        
        // Clear buffer
        uart1_rec_index = 0;
        memset(uart1_rec_string, 0, sizeof(uart1_rec_string));
    }
}

// TCM main task
void tcm_main_task()
{
    // Check start flag
    if(tcm_start_flag) {
        // tcm_start_diagnosis();  // Comment out if not available
        tcm_start_flag = 0;
    }

    // Process TCM state machine
    // tcm_process_task();  // Comment out if not available
}

// Task scheduler configuration (for reference)
// task_t scheduler_task[] = {
//     {esp8266_proc, 2, 0},
//     {usart1_proc, 1, 0},
//     {uart3_proc, 1, 0},
//     {uart8_proc, 1, 0},
//     {uart6_proc, 1, 0},
//     {uart2_proc, 5, 0},
//     {tcm_main_task, 10, 0},
// };

// Timer3 interrupt handler - 1ms interrupt
void TIM3_IRQHandler(void)
{
    if(TIM_GetITStatus(TIM3, TIM_IT_Update) != RESET)
    {
        uwtick++;                // System tick increment
        uart1_rec_tick++;        // UART1 receive timer
        uart3_rec_tick++;        // UART3 control communication timer
        uart6_rec_tick++;        // UART6 K230 communication timer
        uart8_rec_tick++;        // UART8 ESP8266 communication timer
        uart2_rec_tick++;        // UART2 RaspberryPi communication timer
    }
    TIM_ClearITPendingBit(TIM3, TIM_IT_Update);
}

/* Main function */
int main(void)
{
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);  // Set interrupt priority group
    SystemCoreClockUpdate();                         // Update system clock
    USART_Printf_Init(115200);                       // Initialize UART1 for debug output
    Delay_Init();                                    // Initialize delay function
    Tim3_Init(1000, 96-1);                          // Initialize Timer3 for 1ms interrupt

    // Initialize communication modules
    // esp8266_init();     // Comment out if not available
    // onenet_init();      // Comment out if not available
    Usart3_Init();      // Initialize UART3 for PC control communication
    Uart6_Init();       // Initialize UART6 for K230 communication
    Uart8_Init();       // Initialize UART8 for ESP8266 communication

    // Initialize system modules
    // k230_init();        // Comment out if not available
    // display_init();     // Comment out if not available
    // tcm_diagnosis_init(); // Comment out if not available

    scheduler_init();   // Initialize task scheduler
    
    while(1)
    {
        scheduler_run();    // Run task scheduler
    }
}

/* UART6 interrupt handler - K230 communication */
void UART6_IRQHandler(void)
{
    u8 temp = 0;
    if(USART_GetITStatus(UART6, USART_IT_RXNE) != RESET)
    {
        uart6_rec_tick = 0;
        temp = USART_ReceiveData(UART6);

        if(uart6_rec_index < 127) {
            uart6_rec_string[uart6_rec_index] = temp;
            uart6_rec_index++;
        }

        USART_ClearITPendingBit(UART6, USART_IT_RXNE);
    }
}

/* UART8 interrupt handler - ESP8266 communication */
void UART8_IRQHandler(void)
{
    u8 temp = 0;
    if(USART_GetITStatus(UART8, USART_IT_RXNE) != RESET)
    {
        uart8_rec_tick = 0;
        temp = USART_ReceiveData(UART8);

        if(uart8_rec_index < 127) {
            uart8_rec_string[uart8_rec_index] = temp;
            uart8_rec_index++;
        }

        USART_ClearITPendingBit(UART8, USART_IT_RXNE);
    }
}

/* USART1 interrupt handler - PC debug communication */
void USART1_IRQHandler(void)
{
    u8 temp = 0;
    if(USART_GetITStatus(USART1, USART_IT_RXNE) != RESET)
    {
        uart1_rec_tick = 0;
        temp = USART_ReceiveData(USART1);

        if(uart1_rec_index < 63) {
            uart1_rec_string[uart1_rec_index] = temp;
            uart1_rec_index++;
        }

        USART_ClearITPendingBit(USART1, USART_IT_RXNE);
    }
}

/* USART3 interrupt handler - PC control communication */
void USART3_IRQHandler(void)
{
    u8 temp = 0;
    if(USART_GetITStatus(USART3, USART_IT_RXNE) != RESET)
    {
        uart3_rec_tick = 0;
        temp = USART_ReceiveData(USART3);

        if(uart3_rec_index < 127) {
            uart3_rec_string[uart3_rec_index] = temp;
            uart3_rec_index++;
        }

        USART_ClearITPendingBit(USART3, USART_IT_RXNE);
    }
}

/* USART2 interrupt handler - RaspberryPi communication */
void USART2_IRQHandler(void)
{
    u8 temp = 0;
    if(USART_GetITStatus(USART2, USART_IT_RXNE) != RESET)
    {
        uart2_rec_tick = 0;
        temp = USART_ReceiveData(USART2);

        if(uart2_rec_index < 255) {
            uart2_rec_string[uart2_rec_index] = temp;
            uart2_rec_index++;
        }

        USART_ClearITPendingBit(USART2, USART_IT_RXNE);
    }
}
