#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K230主程序 - UTF-8编码版本
兼容K230 CanMV环境，无any()方法依赖
支持与CH32V307 UART6通信
"""

import time
import os
from machine import UART

class K230Main:
    def __init__(self):
        """初始化K230程序"""
        print("=== K230主程序启动 ===")

        # UART初始化 - 对应CH32V307的UART6(PC0/PC1)
        try:
            self.uart = UART(1, 115200)
            print(" UART初始化成功 (115200波特率)")
        except Exception as e:
            print(f" UART初始化失败: {e}")
            self.uart = None

        # 计数器
        self.photo_count = 0
        self.test_count = 0

        print(" K230程序初始化完成")
        self.send_message("INIT_OK")

    def send_message(self, msg):
        """发送消息到UART"""
        if not self.uart:
            print(f"[无UART] 应发送: {msg}")
            return

        try:
            data = f"{msg}\r\n".encode('utf-8')
            self.uart.write(data)
            print(f"发送: {msg}")
        except Exception as e:
            print(f"发送失败: {e}")

    def read_uart_data(self):
        """读取UART数据 - 不使用any()方法"""
        if not self.uart:
            return None

        try:
            # 直接读取数据，不检查any()
            data = self.uart.read(128)  # 读取最多128字节
            return data
        except Exception as e:
            return None

    def create_test_photo(self):
        """创建测试照片文件"""
        try:
            timestamp = time.ticks_ms()
            filename = f"photo_{self.photo_count:04d}_{timestamp}.txt"

            # 创建测试文件
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"K230测试照片 #{self.photo_count}\n")
                f.write(f"时间戳: {timestamp}\n")
                f.write(f"文件名: {filename}\n")
                f.write(f"UART通信测试成功\n")
                f.write(f"编码: UTF-8\n")

            self.photo_count += 1
            print(f" 测试照片已创建: {filename}")
            self.send_message(f"PHOTO_OK:{filename}")
            return True

        except Exception as e:
            print(f" 照片创建失败: {e}")
            self.send_message("PHOTO_FAIL")
            return False

    def process_command(self, cmd):
        """处理接收到的命令"""
        cmd = cmd.strip().lower()
        print(f"处理命令: '{cmd}'")

        if cmd == "photo":
            self.create_test_photo()

        elif cmd == "test":
            self.test_count += 1
            self.send_message(f"TEST_OK:{self.test_count}")

        elif cmd == "ping":
            self.send_message("PONG")

        elif cmd == "count":
            self.send_message(f"COUNT:{self.photo_count}")

        elif cmd == "status":
            self.send_message(f"STATUS_OK:PHOTOS_{self.photo_count}_TESTS_{self.test_count}")

        elif cmd == "reset":
            self.photo_count = 0
            self.test_count = 0
            self.send_message("RESET_OK")

        elif cmd == "hello":
            self.send_message("HELLO_K230_UTF8")

        elif cmd == "info":
            self.send_message(f"INFO:K230_CANMV_UTF8_PHOTOS_{self.photo_count}")

        else:
            self.send_message(f"UNKNOWN:{cmd}")

    def main_loop(self):
        """主循环"""
        print(" K230主程序开始运行")
        self.send_message("K230_READY")

        buffer = ""
        loop_count = 0
        last_heartbeat = 0

        while True:
            try:
                # 读取UART数据
                data = self.read_uart_data()

                if data:
                    try:
                        # 处理接收到的数据
                        if isinstance(data, bytes):
                            text = data.decode('utf-8', errors='ignore')
                        else:
                            text = str(data)

                        buffer += text

                        # 处理完整的命令行
                        while '\n' in buffer:
                            line, buffer = buffer.split('\n', 1)
                            command = line.strip('\r\n ')

                            if command:
                                print(f"接收到: {command}")
                                self.process_command(command)

                    except Exception as e:
                        print(f"数据处理错误: {e}")

                # 心跳和状态显示
                loop_count += 1
                current_time = time.ticks_ms()

                # 每10秒发送一次心跳
                if current_time - last_heartbeat > 10000:
                    print(f"心跳 - 照片: {self.photo_count}, 测试: {self.test_count}, 循环: {loop_count}")
                    self.send_message("HEARTBEAT")
                    last_heartbeat = current_time

                time.sleep_ms(50)  # 50ms延时

            except KeyboardInterrupt:
                print("\n程序被用户中断")
                break
            except Exception as e:
                print(f"主循环错误: {e}")
                time.sleep_ms(100)

        print(" K230程序结束")

def main():
    """主函数"""
    print("=" * 50)
    print("    K230 CanMV 主程序 (UTF-8编码)")
    print("=" * 50)
    print("支持命令:")
    print("  photo  - 创建测试照片文件")
    print("  test   - 测试连接")
    print("  ping   - Ping测试")
    print("  count  - 查询照片数量")
    print("  status - 查询状态")
    print("  reset  - 重置计数器")
    print("  hello  - 问候测试")
    print("  info   - 系统信息")
    print("=" * 50)

    try:
        app = K230Main()
        app.main_loop()
    except Exception as e:
        print(f"程序异常: {e}")
        try:
            import sys
            sys.print_exception(e)
        except:
            pass

if __name__ == "__main__":
    main()
