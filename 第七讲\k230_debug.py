#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K230璋冭瘯鐗堟湰 - 璇婃柇UART閫氫俊闂�棰�
"""

import time
import os
from machine import UART

class K230Debug:
    def __init__(self):
        """鍒濆�嬪寲K230璋冭瘯绋嬪簭"""
        print("=== K230璋冭瘯绋嬪簭鍚�鍔� ===")

        # UART鍒濆�嬪寲 - 瀵瑰簲CH32V307鐨刄ART6(PC0/PC1)
        try:
            self.uart = UART(1, 115200)
            print("? UART鍒濆�嬪寲鎴愬姛 (115200娉㈢壒鐜�)")
            print(f"? UART瀵硅薄: {self.uart}")
        except Exception as e:
            print(f"? UART鍒濆�嬪寲澶辫触: {e}")
            self.uart = None

        # 璁℃暟鍣�
        self.photo_count = 0
        self.test_count = 0
        self.receive_count = 0

        print("? K230璋冭瘯绋嬪簭鍒濆�嬪寲瀹屾垚")
        self.send_message("DEBUG_INIT_OK")

    def send_message(self, msg):
        """鍙戦€佹秷鎭�鍒癠ART"""
        if not self.uart:
            print(f"[鏃燯ART] 搴斿彂閫�: {msg}")
            return

        try:
            data = f"{msg}\r\n".encode('utf-8')
            self.uart.write(data)
            print(f"? 鍙戦€佹垚鍔�: {msg} (瀛楄妭鏁�: {len(data)})")
        except Exception as e:
            print(f"? 鍙戦€佸け璐�: {e}")

    def read_uart_data_debug(self):
        """璋冭瘯鐗圲ART鏁版嵁璇诲彇"""
        if not self.uart:
            return None

        try:
            # 灏濊瘯澶氱�嶈�诲彇鏂规硶
            print("鈫� 灏濊瘯璇诲彇UART鏁版嵁...")

            # 鏂规硶1: 鐩存帴璇诲彇
            data1 = self.uart.read(1)
            if data1:
                print(f"? 鏂规硶1璇诲彇鍒�: {data1} (绫诲瀷: {type(data1)})")
                return data1

            # 鏂规硶2: 璇诲彇鏇村�氬瓧鑺�
            data2 = self.uart.read(128)
            if data2:
                print(f"? 鏂规硶2璇诲彇鍒�: {data2} (绫诲瀷: {type(data2)})")
                return data2

            # 鏂规硶3: 灏濊瘯readline
            try:
                data3 = self.uart.readline()
                if data3:
                    print(f"? 鏂规硶3璇诲彇鍒�: {data3} (绫诲瀷: {type(data3)})")
                    return data3
            except:
                pass

            return None

        except Exception as e:
            print(f"? 璇诲彇寮傚父: {e}")
            return None

    def process_command(self, cmd):
        """澶勭悊鎺ユ敹鍒扮殑鍛戒护"""
        cmd = cmd.strip().lower()
        print(f"? 澶勭悊鍛戒护: '{cmd}'")

        if cmd == "photo":
            self.photo_count += 1
            print(f"? 鐓х墖鍛戒护鎵ц�� (鎬绘暟: {self.photo_count})")
            self.send_message(f"PHOTO_OK:{self.photo_count}")

        elif cmd == "test":
            self.test_count += 1
            print(f"? 娴嬭瘯鍛戒护鎵ц�� (鎬绘暟: {self.test_count})")
            self.send_message(f"TEST_OK:{self.test_count}")

        elif cmd == "ping":
            print("? Ping鍛戒护鎵ц��")
            self.send_message("PONG")

        elif cmd == "debug":
            print("? 璋冭瘯淇℃伅:")
            print(f"  - 鐓х墖鏁�: {self.photo_count}")
            print(f"  - 娴嬭瘯鏁�: {self.test_count}")
            print(f"  - 鎺ユ敹鏁�: {self.receive_count}")
            print(f"  - UART鐘舵€�: {self.uart}")
            self.send_message(f"DEBUG_INFO:P{self.photo_count}_T{self.test_count}_R{self.receive_count}")

        else:
            print(f"? 鏈�鐭ュ懡浠�: {cmd}")
            self.send_message(f"UNKNOWN:{cmd}")

    def main_loop(self):
        """涓诲惊鐜� - 璋冭瘯鐗堟湰"""
        print("? K230璋冭瘯绋嬪簭寮€濮嬭繍琛�")
        self.send_message("DEBUG_READY")

        buffer = ""
        loop_count = 0
        last_heartbeat = 0
        last_status = 0

        while True:
            try:
                # 璇诲彇UART鏁版嵁
                data = self.read_uart_data_debug()

                if data:
                    self.receive_count += 1
                    print(f"? 绗瑊self.receive_count}娆℃帴鏀跺埌鏁版嵁")

                    try:
                        # 澶勭悊鎺ユ敹鍒扮殑鏁版嵁
                        if isinstance(data, bytes):
                            text = data.decode('utf-8', errors='ignore')
                        else:
                            text = str(data)

                        print(f"? 瑙ｇ爜鍚庢枃鏈�: '{text}' (闀垮害: {len(text)})")
                        buffer += text

                        # 澶勭悊瀹屾暣鐨勫懡浠よ��
                        while '\n' in buffer:
                            line, buffer = buffer.split('\n', 1)
                            command = line.strip('\r\n ')

                            if command:
                                print(f"? 鎻愬彇鍛戒护: '{command}'")
                                self.process_command(command)

                    except Exception as e:
                        print(f"? 鏁版嵁澶勭悊閿欒��: {e}")

                # 鐘舵€佹樉绀哄拰蹇冭烦
                loop_count += 1
                current_time = time.ticks_ms()

                # 姣�5绉掓樉绀虹姸鎬�
                if current_time - last_status > 5000:
                    print(f"鈫� 鐘舵€�: 寰�鐜瘂loop_count}, 鎺ユ敹{self.receive_count}娆�, 鐓х墖{self.photo_count}, 娴嬭瘯{self.test_count}")
                    last_status = current_time

                # 姣�15绉掑彂閫佸績璺�
                if current_time - last_heartbeat > 15000:
                    print(f"? 蹇冭烦鍙戦€�")
                    self.send_message("DEBUG_HEARTBEAT")
                    last_heartbeat = current_time

                time.sleep_ms(100)  # 100ms寤舵椂锛屾洿棰戠箒妫€鏌�

            except KeyboardInterrupt:
                print("\n绋嬪簭琚�鐢ㄦ埛涓�鏂�")
                break
            except Exception as e:
                print(f"? 涓诲惊鐜�閿欒��: {e}")
                time.sleep_ms(200)

        print("? K230璋冭瘯绋嬪簭缁撴潫")

def main():
    """涓诲嚱鏁�"""
    print("=" * 60)
    print("    K230 CanMV 璋冭瘯绋嬪簭 (UTF-8缂栫爜)")
    print("=" * 60)
    print("璋冭瘯鍔熻兘:")
    print("  - 璇︾粏鐨刄ART璇诲彇鏃ュ織")
    print("  - 澶氱�嶆暟鎹�璇诲彇鏂规硶娴嬭瘯")
    print("  - 瀹炴椂鐘舵€佹樉绀�")
    print("  - 澧炲己鐨勯敊璇�澶勭悊")
    print("=" * 60)
    print("鏀�鎸佸懡浠�:")
    print("  photo  - 鍒涘缓鐓х墖")
    print("  test   - 娴嬭瘯杩炴帴")
    print("  ping   - Ping娴嬭瘯")
    print("  debug  - 鏄剧ず璋冭瘯淇℃伅")
    print("=" * 60)

    try:
        app = K230Debug()
        app.main_loop()
    except Exception as e:
        print(f"绋嬪簭寮傚父: {e}")
        try:
            import sys
            sys.print_exception(e)
        except:
            pass

if __name__ == "__main__":
    main()
