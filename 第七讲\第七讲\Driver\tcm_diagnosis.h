#ifndef TCM_DIAGNOSIS_H
#define TCM_DIAGNOSIS_H

#include "config.h"
#include "k230_camera.h"
#include "display_driver.h"

// ????????
typedef enum {
    TCM_STATE_IDLE = 0,
    TCM_STATE_CAPTURING,
    TCM_STATE_UPLOADING,
    TCM_STATE_ANALYZING,
    TCM_STATE_DISPLAYING,
    TCM_STATE_ERROR
} tcm_state_t;

// ????????????
typedef struct {
    u8 header;
    u8 cmd;
    u32 image_size;
    u8 checksum;
    u8 footer;
} rpi_header_t;

// ???????????
#define RPI_CMD_ANALYZE 0x01
#define RPI_CMD_RESULT 0x02
#define RPI_CMD_ERROR 0x03

// ????????
void tcm_diagnosis_init(void);
int tcm_start_diagnosis(void);
void tcm_process_task(void);
int tcm_send_image_to_rpi(const k230_image_t* img);
int tcm_receive_result_from_rpi(tcm_result_t* result);
void tcm_state_machine(void);
void tcm_error_handler(int error_code);

// ???????????
int rpi_send_header(u32 image_size);
int rpi_send_image_data(const u8* data, u32 size);
int rpi_receive_response(tcm_result_t* result);
u8 rpi_calculate_checksum(const u8* data, u32 len);

// ??????
extern tcm_state_t tcm_current_state;
extern u8 tcm_diagnosis_progress;

#endif
