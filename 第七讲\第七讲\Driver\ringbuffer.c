// ringbuffer.c
#include "ringbuffer.h"
#include "debug.h"

void RingBuffer_Init(RingBuffer *rb, uint8_t *pool, uint16_t size) {
    rb->buffer = pool;
    rb->head = 0;
    rb->tail = 0;
    rb->size = 0;
    rb->capacity = size;
}

uint16_t RingBuffer_Put(RingBuffer *rb, const uint8_t *data, uint16_t len) {
    uint16_t available = rb->capacity - rb->size;
    if (available < len) len = available;

    for(uint16_t i=0; i<len; i++) {
        rb->buffer[rb->tail] = data[i];
        rb->tail = (rb->tail + 1) % rb->capacity;
    }
    rb->size += len;
    return len;
}

uint16_t RingBuffer_Get(RingBuffer *rb, uint8_t *data, uint16_t len) {
    if (rb->size < len) len = rb->size;

    for(uint16_t i=0; i<len; i++) {
        data[i] = rb->buffer[rb->head];
        rb->head = (rb->head + 1) % rb->capacity;
    }
    rb->size -= len;
    return len;
}
