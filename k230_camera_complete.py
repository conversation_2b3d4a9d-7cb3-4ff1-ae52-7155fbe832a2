#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K230 Camera Photo Program - Complete Version
Supports both full-featured and simple modes
Compatible with CH32V307 UART communication
"""

import time
import os

# Try to import K230-specific modules
try:
    import sensor
    import image
    from machine import UART
    K230_MODULES_AVAILABLE = True
    print("K230 modules imported successfully")
except ImportError as e:
    print(f"K230 module import failed: {e}")
    print("Running in simulation mode...")
    K230_MODULES_AVAILABLE = False

    # Create mock classes for testing
    class MockUART:
        def __init__(self, port, baudrate, timeout=1000):
            self.port = port
            self.baudrate = baudrate
            print(f"Mock UART{port} initialized, baudrate {baudrate}")

        def write(self, data):
            print(f"Mock UART write: {data}")

        def read(self):
            return b""

        def any(self):
            return False

    class MockSensor:
        @staticmethod
        def reset():
            print("Mock sensor reset")

        @staticmethod
        def set_pixformat(fmt):
            print(f"Mock sensor set pixformat: {fmt}")

        @staticmethod
        def set_framesize(size):
            print(f"Mock sensor set framesize: {size}")

        @staticmethod
        def skip_frames(time=2000):
            print(f"Mock sensor skip frames: {time}ms")

        @staticmethod
        def snapshot():
            return MockImage()

        RGB565 = "RGB565"
        VGA = "VGA"

    class MockImage:
        def __init__(self):
            self._width = 640
            self._height = 480

        def width(self):
            return self._width

        def height(self):
            return self._height

        def save(self, filename, quality=90):
            print(f"Mock image save: {filename}, quality: {quality}")
            # Create a dummy file for testing
            try:
                with open(filename, 'w') as f:
                    f.write("mock image data")
                return True
            except:
                return False

    # Assign mock classes
    UART = MockUART
    sensor = MockSensor()
    image = MockImage

# Configuration
USE_SIMPLE_MODE = False  # Set to True for simple mode, False for full mode
UART_PORT = 1
UART_BAUDRATE = 115200
PHOTO_QUALITY = 90

# Initialize UART for CH32V307 communication
uart = UART(UART_PORT, UART_BAUDRATE, timeout=1000)

# ============================================================================
# SIMPLE MODE FUNCTIONS
# ============================================================================

def simple_setup_camera():
    """Simple camera setup"""
    try:
        print("Setting up camera...")
        if not K230_MODULES_AVAILABLE:
            print("Warning: Running in simulation mode")

        sensor.reset()
        sensor.set_pixformat(sensor.RGB565)
        sensor.set_framesize(sensor.VGA)
        sensor.skip_frames(time=2000)
        print("Camera ready")
        return True
    except Exception as e:
        print("Camera setup failed:", e)
        if not K230_MODULES_AVAILABLE:
            print("Note: This is expected in simulation mode")
        return False

def simple_take_photo():
    """Simple photo capture"""
    try:
        print("Taking photo...")
        img = sensor.snapshot()
        if img:
            filename = "photo_" + str(time.ticks_ms()) + ".jpg"
            img.save(filename, quality=PHOTO_QUALITY)
            print("Photo saved:", filename)
            uart.write(b"PHOTO_OK\n")
            return True
        else:
            print("Photo failed")
            uart.write(b"PHOTO_FAIL\n")
            return False
    except Exception as e:
        print("Photo error:", e)
        uart.write(b"PHOTO_ERROR\n")
        return False

def simple_main():
    """Simple main program"""
    print("K230 Camera Program Starting (Simple Mode)...")
    print("UART1 ready, baudrate", UART_BAUDRATE)
    print("Waiting for CAPTURE command...")
    
    if simple_setup_camera():
        buffer = b""
        
        while True:
            try:
                if uart.any():
                    data = uart.read()
                    if data:
                        buffer += data
                        
                        # Check for CAPTURE command
                        if b'CAPTURE' in buffer:
                            print("CAPTURE command received")
                            simple_take_photo()
                            buffer = b""
                        
                        # Clear buffer if too long
                        if len(buffer) > 100:
                            buffer = b""
                
                time.sleep_ms(10)
                
            except KeyboardInterrupt:
                print("Program stopped by user")
                break
            except Exception as e:
                print("Error:", e)
                time.sleep_ms(100)
    else:
        print("Camera setup failed, exiting")

# ============================================================================
# FULL MODE FUNCTIONS  
# ============================================================================

def full_init_camera():
    """Full camera initialization with detailed error handling"""
    try:
        print("Initializing camera...")
        if not K230_MODULES_AVAILABLE:
            print("Warning: Running in simulation mode - camera functions will be mocked")

        sensor.reset()
        sensor.set_pixformat(sensor.RGB565)
        sensor.set_framesize(sensor.VGA)  # 640x480
        sensor.skip_frames(time=2000)
        print("Camera initialized successfully")
        return True
    except Exception as e:
        print(f"Camera initialization failed: {e}")
        if not K230_MODULES_AVAILABLE:
            print("Note: This is expected in simulation mode")
        return False

def full_capture_and_save():
    """Full photo capture with detailed status reporting"""
    try:
        print("Starting photo capture...")
        
        # Capture image
        img = sensor.snapshot()
        if img:
            # Generate filename with timestamp
            timestamp = time.ticks_ms()
            filename = f"photo_{timestamp}.jpg"
            
            # Save as JPEG to root directory
            img.save(filename, quality=PHOTO_QUALITY)
            
            print(f"Photo captured successfully! Saved as: {filename}")
            print(f"Image size: {img.width()}x{img.height()}")
            
            # Send success response to CH32V307
            uart.write(b"PHOTO_OK\n")
            
            return True
        else:
            print("Photo capture failed: Unable to get image")
            uart.write(b"PHOTO_FAIL\n")
            return False
            
    except Exception as e:
        print(f"Photo capture exception: {e}")
        uart.write(b"PHOTO_ERROR\n")
        return False

def full_process_command(data):
    """Process received commands with detailed parsing"""
    try:
        command = data.decode('utf-8').strip()
        print(f"Received command: {command}")
        
        if "CAPTURE" in command:
            print("Executing photo capture command")
            full_capture_and_save()
        else:
            print(f"Unknown command: {command}")
    except Exception as e:
        print(f"Command processing error: {e}")

def full_main():
    """Full main program with comprehensive features"""
    print("=" * 50)
    print("K230 Camera Photo Program (Full Mode)")
    print("Waiting for CH32V307 photo commands...")
    print("=" * 50)
    print(f"UART{UART_PORT} initialized, baudrate {UART_BAUDRATE}")
    
    # Initialize camera
    if not full_init_camera():
        print("Camera initialization failed, program exit")
        return
    
    # Display current directory files
    print("Current root directory files:")
    try:
        files = os.listdir('/')
        for f in files:
            print(f"  {f}")
    except:
        print("  Unable to list files")
    
    print("\nWaiting for photo commands...")
    
    # Main loop
    rx_buffer = b""
    
    while True:
        try:
            # Check if data is received
            if uart.any():
                # Read data
                data = uart.read()
                if data:
                    rx_buffer += data
                    
                    # Check for complete commands (ending with newline)
                    while b'\n' in rx_buffer:
                        line, rx_buffer = rx_buffer.split(b'\n', 1)
                        if line:
                            full_process_command(line)
                    
                    # Also check for commands without newline
                    if b'CAPTURE' in rx_buffer:
                        full_process_command(rx_buffer)
                        rx_buffer = b""
            
            # Short delay
            time.sleep_ms(10)
            
        except KeyboardInterrupt:
            print("\nProgram interrupted by user")
            break
        except Exception as e:
            print(f"Main loop exception: {e}")
            time.sleep_ms(100)
    
    print("Program ended")

# ============================================================================
# TEST FUNCTIONS (Full mode only)
# ============================================================================

def test_camera():
    """Test camera functionality"""
    print("Testing camera functionality...")
    
    if full_init_camera():
        print("Camera test successful")
        
        # Test photo capture
        img = sensor.snapshot()
        if img:
            print(f"Test photo successful: {img.width()}x{img.height()}")
            img.save("test.jpg")
            print("Test image saved as test.jpg")
        else:
            print("Test photo failed")
    else:
        print("Camera test failed")

def test_uart():
    """Test UART communication"""
    print("Testing UART communication...")
    uart.write(b"K230_READY\n")
    print("Test data sent")

# ============================================================================
# MAIN PROGRAM ENTRY
# ============================================================================

def main():
    """Main program entry point"""
    print("K230 Camera Program Starting...")
    print(f"Mode: {'Simple' if USE_SIMPLE_MODE else 'Full'}")
    print("-" * 30)
    
    if USE_SIMPLE_MODE:
        simple_main()
    else:
        full_main()

# Run program
if __name__ == "__main__":
    # Uncomment the following lines to run tests
    # test_camera()
    # test_uart()
    
    # Run main program
    main()
