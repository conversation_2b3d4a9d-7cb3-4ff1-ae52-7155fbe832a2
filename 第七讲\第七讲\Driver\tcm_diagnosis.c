#include "tcm_diagnosis.h"
#include "uart_utf8.h"
#include "string.h"
#include "stdio.h"

// ??????????
extern unsigned long int uwtick;

// ??????
tcm_state_t tcm_current_state = TCM_STATE_IDLE;
u8 tcm_diagnosis_progress = 0;
static k230_image_t captured_image;
static tcm_result_t diagnosis_result;
static u32 rpi_timeout_start = 0;

void tcm_diagnosis_init(void) {
    tcm_current_state = TCM_STATE_IDLE;
    tcm_diagnosis_progress = 0;
    Usart2_Init(); // ???UART2??????????
    memset(&captured_image, 0, sizeof(k230_image_t));
    memset(&diagnosis_result, 0, sizeof(tcm_result_t));
}

int tcm_start_diagnosis(void) {
    if (tcm_current_state != TCM_STATE_IDLE) {
        return ERR_COMM_FAIL;
    }
    
    tcm_current_state = TCM_STATE_CAPTURING;
    tcm_diagnosis_progress = 0;
    display_capture_page();
    
    return ERR_OK;
}

void tcm_process_task(void) {
    tcm_state_machine();
}

void tcm_state_machine(void) {
    static u32 state_start_time = 0;
    
    switch (tcm_current_state) {
        case TCM_STATE_IDLE:
            // ?????????
            break;
            
        case TCM_STATE_CAPTURING:
            if (state_start_time == 0) {
                state_start_time = uwtick;
            }
            
            // ???2????????
            if (uwtick - state_start_time > 2000) {
                int ret = k230_capture_image(&captured_image);
                if (ret == ERR_OK) {
                    tcm_current_state = TCM_STATE_UPLOADING;
                    tcm_diagnosis_progress = 25;
                    display_analysis_page(tcm_diagnosis_progress);
                    state_start_time = 0;
                } else {
                    tcm_error_handler(ret);
                }
            }
            break;
            
        case TCM_STATE_UPLOADING:
            {
                int ret = tcm_send_image_to_rpi(&captured_image);
                if (ret == ERR_OK) {
                    tcm_current_state = TCM_STATE_ANALYZING;
                    tcm_diagnosis_progress = 50;
                    display_analysis_page(tcm_diagnosis_progress);
                    rpi_timeout_start = uwtick;
                } else {
                    tcm_error_handler(ret);
                }
            }
            break;
            
        case TCM_STATE_ANALYZING:
            // ???????
            if (tcm_diagnosis_progress < 90) {
                tcm_diagnosis_progress += 1;
                if (tcm_diagnosis_progress % 10 == 0) {
                    display_analysis_page(tcm_diagnosis_progress);
                }
            }
            
            // ?????????????(?????)
            // int ret = tcm_receive_result_from_rpi(&diagnosis_result);
            int ret = ERR_OK; // ????????
            if (ret == ERR_OK) {
                tcm_current_state = TCM_STATE_DISPLAYING;
                tcm_diagnosis_progress = 100;
            } else if (uwtick - rpi_timeout_start > TCM_DIAGNOSIS_TIMEOUT_MS) {
                tcm_error_handler(ERR_TIMEOUT);
            }
            break;
            
        case TCM_STATE_DISPLAYING:
            display_show_tcm_result(&diagnosis_result);
            // ???5??????????
            if (state_start_time == 0) {
                state_start_time = uwtick;
            }
            if (uwtick - state_start_time > 5000) {
                tcm_current_state = TCM_STATE_IDLE;
                display_main_page();
                state_start_time = 0;
            }
            break;
            
        case TCM_STATE_ERROR:
            // ???????????????
            if (state_start_time == 0) {
                state_start_time = uwtick;
            }
            if (uwtick - state_start_time > 3000) {
                tcm_current_state = TCM_STATE_IDLE;
                display_main_page();
                state_start_time = 0;
            }
            break;
    }
}

int tcm_send_image_to_rpi(const k230_image_t* img) {
    if (!img || !img->data || img->size == 0) {
        return ERR_INVALID_PARAM;
    }
    
    // ??????????
    int ret = rpi_send_header(img->size);
    if (ret != ERR_OK) return ret;
    
    // ???????????
    ret = rpi_send_image_data(img->data, img->size);
    return ret;
}

int rpi_send_header(u32 image_size) {
    rpi_header_t header;
    header.header = PROTOCOL_HEADER;
    header.cmd = RPI_CMD_ANALYZE;
    header.image_size = image_size;
    header.checksum = rpi_calculate_checksum((u8*)&header, sizeof(header) - 2);
    header.footer = PROTOCOL_FOOTER;
    
    // ???UART2????
    for (u8 i = 0; i < sizeof(rpi_header_t); i++) {
        USART_SendData(USART2, ((u8*)&header)[i]);
        while (USART_GetFlagStatus(USART2, USART_FLAG_TC) == 0);
    }
    
    return ERR_OK;
}

int rpi_send_image_data(const u8* data, u32 size) {
    for (u32 i = 0; i < size; i++) {
        USART_SendData(USART2, data[i]);
        while (USART_GetFlagStatus(USART2, USART_FLAG_TC) == 0);
    }
    return ERR_OK;
}

u8 rpi_calculate_checksum(const u8* data, u32 len) {
    u8 checksum = 0;
    for (u32 i = 0; i < len; i++) {
        checksum ^= data[i];
    }
    return checksum;
}

void tcm_error_handler(int error_code) {
    tcm_current_state = TCM_STATE_ERROR;
    
    char error_msg[64];
    switch (error_code) {
        case ERR_TIMEOUT:
            strcpy(error_msg, "???????");
            break;
        case ERR_COMM_FAIL:
            strcpy(error_msg, "??????");
            break;
        case ERR_INVALID_PARAM:
            strcpy(error_msg, "????????");
            break;
        default:
            strcpy(error_msg, "???????");
            break;
    }
    
    display_clear_screen();
    display_show_text(50, 100, "????:", 0xF800);
    display_show_text(50, 130, error_msg, 0xF800);
    display_show_text(50, 160, "3???????", 0x07E0);
}
