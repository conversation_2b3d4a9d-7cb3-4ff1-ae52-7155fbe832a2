#ifndef K230_CAMERA_H
#define K230_CAMERA_H

#include "config_utf8.h"
#include "debug.h"

// K230???????
typedef enum {
    K230_STATE_IDLE = 0,
    K230_STATE_CAPTURING,
    K230_STATE_STREAMING,
    K230_STATE_ERROR
} k230_state_t;

// K230?????
typedef struct {
    u8 header;
    u8 cmd;
    u16 length;
    u8 data[MAX_PACKET_SIZE];
    u8 checksum;
    u8 footer;
} k230_packet_t;

// K230??????
typedef struct {
    u16 width;
    u16 height;
    u8 format;
    u32 size;
    u8* data;
} k230_image_t;

// ????????
void k230_init(void);
void k230_reset_receive_state(void);
int k230_capture_image(k230_image_t* img);
int k230_start_stream(void);
int k230_stop_stream(void);
int k230_send_command(u8 cmd, u8* data, u16 len);
int k230_receive_response(k230_packet_t* packet);
void k230_process_data(void);
void k230_uart_rx_handler(u8 data);
u8 k230_calculate_checksum(u8* data, u16 len);

// ??????????
extern k230_state_t k230_state;
extern k230_image_t current_image;

#endif
