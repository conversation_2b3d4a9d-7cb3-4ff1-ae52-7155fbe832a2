/*
 * UART Driver - UTF-8 Version
 * CH32V307VCT6 Multi-UART Communication
 * Supports UART1, USART2, USART3, UART6, UART8
 */

#include "ch32v30x.h"
#include "config_utf8.h"
#include <string.h>

/* USART2 Initialization - RaspberryPi Communication (PA2/PA3) */
void Usart2_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    USART_InitTypeDef USART_InitStructure;
    NVIC_InitTypeDef NVIC_InitStructure;

    // Enable clocks
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART2, ENABLE);
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);

    // Configure GPIO pins
    GPIO_InitStructure.GPIO_Pin = UART2_TX_PIN | UART2_RX_PIN;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
    GPIO_Init(UART2_GPIO_PORT, &GPIO_InitStructure);

    // Configure USART
    USART_InitStructure.USART_BaudRate = UART_BAUDRATE;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStructure.USART_Mode = USART_Mode_Tx | USART_Mode_Rx;
    USART_Init(USART2, &USART_InitStructure);

    // Enable receive interrupt
    USART_ITConfig(USART2, USART_IT_RXNE, ENABLE);

    // Configure NVIC
    NVIC_InitStructure.NVIC_IRQChannel = USART2_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);

    // Enable USART
    USART_Cmd(USART2, ENABLE);
    USART_ClearFlag(USART2, USART_FLAG_TC);
}

/* USART3 Initialization - PC Control Communication (PB10/PB11) */
void Usart3_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    USART_InitTypeDef USART_InitStructure;
    NVIC_InitTypeDef NVIC_InitStructure;

    // Enable clocks
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART3, ENABLE);
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB, ENABLE);

    // Configure GPIO pins
    GPIO_InitStructure.GPIO_Pin = UART3_TX_PIN | UART3_RX_PIN;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
    GPIO_Init(UART3_GPIO_PORT, &GPIO_InitStructure);

    // Configure USART
    USART_InitStructure.USART_BaudRate = UART_BAUDRATE;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStructure.USART_Mode = USART_Mode_Tx | USART_Mode_Rx;
    USART_Init(USART3, &USART_InitStructure);

    // Enable receive interrupt
    USART_ITConfig(USART3, USART_IT_RXNE, ENABLE);

    // Configure NVIC
    NVIC_InitStructure.NVIC_IRQChannel = USART3_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 2;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);

    // Enable USART
    USART_Cmd(USART3, ENABLE);
    USART_ClearFlag(USART3, USART_FLAG_TC);
}

/* UART6 Initialization - K230 Communication (PC0/PC1) */
void Uart6_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    USART_InitTypeDef USART_InitStructure;
    NVIC_InitTypeDef NVIC_InitStructure;

    // Enable clocks
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_UART6, ENABLE);
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOC, ENABLE);

    // Configure GPIO pins
    GPIO_InitStructure.GPIO_Pin = UART6_TX_PIN | UART6_RX_PIN;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
    GPIO_Init(UART6_GPIO_PORT, &GPIO_InitStructure);

    // Configure UART
    USART_InitStructure.USART_BaudRate = UART_BAUDRATE;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStructure.USART_Mode = USART_Mode_Tx | USART_Mode_Rx;
    USART_Init(UART6, &USART_InitStructure);

    // Enable receive interrupt
    USART_ITConfig(UART6, USART_IT_RXNE, ENABLE);

    // Configure NVIC
    NVIC_InitStructure.NVIC_IRQChannel = UART6_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 3;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);

    // Enable UART
    USART_Cmd(UART6, ENABLE);
    USART_ClearFlag(UART6, USART_FLAG_TC);
}

/* UART8 Initialization - ESP8266 Communication (PE0/PE1) */
void Uart8_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    USART_InitTypeDef USART_InitStructure;
    NVIC_InitTypeDef NVIC_InitStructure;

    // Enable clocks
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_UART8, ENABLE);
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOE, ENABLE);

    // Configure GPIO pins
    GPIO_InitStructure.GPIO_Pin = UART8_TX_PIN | UART8_RX_PIN;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
    GPIO_Init(UART8_GPIO_PORT, &GPIO_InitStructure);

    // Configure UART
    USART_InitStructure.USART_BaudRate = UART_BAUDRATE;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStructure.USART_Mode = USART_Mode_Tx | USART_Mode_Rx;
    USART_Init(UART8, &USART_InitStructure);

    // Enable receive interrupt
    USART_ITConfig(UART8, USART_IT_RXNE, ENABLE);

    // Configure NVIC
    NVIC_InitStructure.NVIC_IRQChannel = UART8_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 2;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);

    // Enable UART
    USART_Cmd(UART8, ENABLE);
    USART_ClearFlag(UART8, USART_FLAG_TC);
}

/* UART Send String Functions */

/* Send string via USART1 (Debug) */
void usart1_send_string(u8* string, u8 len)
{
    u8 i;
    for(i = 0; i < len; i++)
    {
        USART_SendData(USART1, string[i]);
        while(USART_GetFlagStatus(USART1, USART_FLAG_TC) == 0);
    }
}

/* Send string via USART2 (RaspberryPi) */
void usart2_send_string(u8* string, u8 len)
{
    u8 i;
    for(i = 0; i < len; i++)
    {
        USART_SendData(USART2, string[i]);
        while(USART_GetFlagStatus(USART2, USART_FLAG_TC) == 0);
    }
}

/* Send string via USART3 (Control) */
void usart3_send_string(u8* string, u8 len)
{
    u8 i;
    for(i = 0; i < len; i++)
    {
        USART_SendData(USART3, string[i]);
        while(USART_GetFlagStatus(USART3, USART_FLAG_TC) == 0);
    }
}

/* Send string via UART6 (K230) */
void uart6_send_string(u8* string, u8 len)
{
    u8 i;
    for(i = 0; i < len; i++)
    {
        USART_SendData(UART6, string[i]);
        while(USART_GetFlagStatus(UART6, USART_FLAG_TC) == 0);
    }
}

/* Send string via UART8 (ESP8266) */
void uart8_send_string(u8* string, u8 len)
{
    u8 i;
    for(i = 0; i < len; i++)
    {
        USART_SendData(UART8, string[i]);
        while(USART_GetFlagStatus(UART8, USART_FLAG_TC) == 0);
    }
}

/* Utility Functions */

/* Send formatted string via UART6 */
void uart6_printf(const char* format, ...)
{
    char buffer[128];
    va_list args;
    va_start(args, format);
    int len = vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    
    if(len > 0 && len < sizeof(buffer))
    {
        uart6_send_string((u8*)buffer, len);
    }
}

/* Send AT command via UART8 */
void uart8_send_at_command(const char* cmd)
{
    uart8_send_string((u8*)cmd, strlen(cmd));
    uart8_send_string((u8*)"\r\n", 2);
}

/* Check if UART has data available */
u8 uart_data_available(USART_TypeDef* uart)
{
    return (uart->STATR & USART_FLAG_RXNE) ? 1 : 0;
}

/* Flush UART receive buffer */
void uart_flush_rx_buffer(USART_TypeDef* uart)
{
    while(uart_data_available(uart))
    {
        (void)USART_ReceiveData(uart);
    }
}
