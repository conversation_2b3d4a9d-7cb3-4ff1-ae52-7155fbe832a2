#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K230 Basic Photo Program - Minimal Version (UTF-8)
Receive UART command "photo" to take photos and save
"""

import time
from machine import UART
import sensor
import image

# Global variables
uart = None
photo_count = 0

def init_system():
    """Initialize system"""
    global uart
    
    # Initialize UART - corresponds to CH32V307 UART6(PC0/PC1)
    uart = UART(1, 115200, 8, None, 1, timeout=1000, read_buf_len=1024)
    
    # Initialize camera
    sensor.reset()
    sensor.set_pixformat(sensor.RGB565)
    sensor.set_framesize(sensor.QVGA)  # 320x240
    sensor.skip_frames(time=1000)
    
    print("K230 initialization completed")
    uart_send("INIT_OK")

def uart_send(msg):
    """Send UART message"""
    global uart
    try:
        uart.write(f"{msg}\r\n")
        print(f"Send: {msg}")
    except:
        print("UART send failed")

def take_photo():
    """Take photo and save"""
    global photo_count
    try:
        # Take photo
        img = sensor.snapshot()
        
        # Generate filename
        filename = f"pic_{photo_count:03d}.jpg"
        
        # Save
        img.save(filename)
        photo_count += 1
        
        print(f"Photo taken successfully: {filename}")
        uart_send(f"PHOTO_OK:{filename}")
        return True
        
    except Exception as e:
        print(f"Photo failed: {e}")
        uart_send("PHOTO_FAIL")
        return False

def process_cmd(cmd):
    """Process command"""
    cmd = cmd.strip().lower()
    
    if cmd == "photo":
        take_photo()
    elif cmd == "test":
        uart_send("TEST_OK")
    elif cmd == "count":
        uart_send(f"COUNT:{photo_count}")
    else:
        uart_send(f"UNKNOWN:{cmd}")

def main():
    """Main program"""
    global uart
    
    print("=== K230 Photo Program ===")
    
    # Initialize
    init_system()
    
    # Main loop
    buffer = ""
    while True:
        try:
            # Check UART data
            if uart.any():
                data = uart.read()
                if data:
                    buffer += data.decode('utf-8', errors='ignore')
                    
                    # Process complete commands
                    while '\n' in buffer:
                        line, buffer = buffer.split('\n', 1)
                        cmd = line.strip('\r\n ')
                        if cmd:
                            print(f"Received: {cmd}")
                            process_cmd(cmd)
            
            time.sleep_ms(10)
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"Error: {e}")
            time.sleep_ms(100)
    
    print("Program ended")

if __name__ == "__main__":
    main()
