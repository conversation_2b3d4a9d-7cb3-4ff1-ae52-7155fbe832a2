#ifndef DISPLAY_DRIVER_H
#define DISPLAY_DRIVER_H

#include "config.h"
#include "debug.h"

// ?????????
typedef enum {
    PAGE_MAIN = 0,      // ??????
    PAGE_CAPTURE,       // ???????
    PAGE_ANALYSIS,      // ????????
    PAGE_RESULT,        // ???????
    PAGE_SETTINGS       // ???????
} display_page_t;

// ????????
typedef struct {
    u8 header;
    u8 cmd;
    u16 length;
    u8 data[256];
    u8 checksum;
    u8 footer;
} display_packet_t;

// ???????????
typedef struct {
    char tongue_color[32];    // ???
    char tongue_coating[32];  // ???
    char moisture[32];        // ????
    char thickness[32];       // ??
    char diagnosis[128];      // ?????
    u8 confidence;           // ?????
} tcm_result_t;

// ????????
void display_init(void);
int display_set_page(display_page_t page);
int display_show_text(u16 x, u16 y, const char* text, u16 color);
int display_show_image(u16 x, u16 y, u16 width, u16 height, u8* img_data);
int display_clear_screen(void);
int display_show_tcm_result(const tcm_result_t* result);
int display_show_progress(u8 percent);
int display_send_command(u8 cmd, u8* data, u16 len);
void display_process_response(void);
int display_handle_touch_event(u16 x, u16 y);

// ???????????
void display_main_page(void);
void display_capture_page(void);
void display_analysis_page(u8 progress);
void display_result_page(const tcm_result_t* result);

// ??????
extern display_page_t current_page;

#endif
