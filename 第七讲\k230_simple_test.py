#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K230 Simple Test - Minimal UART Test
Step by step UART initialization
"""

import time
from machine import UART

def test_uart_init():
    """Test UART initialization with different parameters"""
    print("Testing UART initialization...")
    
    # Try different UART initialization methods
    uart = None
    
    try:
        # Method 1: Basic parameters
        print("Method 1: Basic UART(1, 115200)")
        uart = UART(1, 115200)
        print("? Method 1 successful")
        return uart
    except Exception as e:
        print(f"? Method 1 failed: {e}")
    
    try:
        # Method 2: With baudrate keyword
        print("Method 2: UART(1, baudrate=115200)")
        uart = UART(1, baudrate=115200)
        print("? Method 2 successful")
        return uart
    except Exception as e:
        print(f"? Method 2 failed: {e}")
    
    try:
        # Method 3: Minimal parameters
        print("Method 3: UART(1)")
        uart = UART(1)
        print("? Method 3 successful")
        return uart
    except Exception as e:
        print(f"? Method 3 failed: {e}")
    
    try:
        # Method 4: Different UART port
        print("Method 4: UART(0, 115200)")
        uart = UART(0, 115200)
        print("? Method 4 successful")
        return uart
    except Exception as e:
        print(f"? Method 4 failed: {e}")
    
    print("All UART initialization methods failed")
    return None

def simple_test():
    """Simple test without UART"""
    print("=== K230 Simple Test ===")
    print("Testing basic functionality...")
    
    # Test 1: Basic operations
    try:
        photo_count = 0
        filename = f"test_{photo_count:03d}.txt"
        
        with open(filename, 'w') as f:
            f.write("K230 test file\n")
            f.write(f"Time: {time.ticks_ms()}\n")
        
        print(f"? File creation test passed: {filename}")
        photo_count += 1
        
    except Exception as e:
        print(f"? File creation test failed: {e}")
    
    # Test 2: UART initialization
    uart = test_uart_init()
    
    if uart:
        try:
            # Test UART send
            uart.write("TEST_MESSAGE\r\n")
            print("? UART send test passed")
            
            # Simple loop
            for i in range(5):
                print(f"Loop {i+1}/5")
                
                # Check for UART data
                if uart.any():
                    data = uart.read()
                    if data:
                        print(f"Received: {data}")
                
                time.sleep_ms(1000)
            
            print("? Main loop test passed")
            
        except Exception as e:
            print(f"? UART operation failed: {e}")
    
    print("=== Test completed ===")

def main():
    """Main function"""
    try:
        simple_test()
    except Exception as e:
        print(f"Program exception: {e}")
        import sys
        sys.print_exception(e)

if __name__ == "__main__":
    main()
