/*
 * CH32V307VCT6 Main Program - UTF-8 Version
 * Multi-UART Communication System
 * Supports PC control, K230 communication, and device management
 */

/* Header files */
#include "debug.h"      // Debug utilities
#include "timer.h"      // Timer functions
#include "uart.h"       // UART communication
#include "string.h"     // String operations
#include "esp8266.h"    // WiFi module
#include "config.h"     // Configuration
#include "k230_camera.h"        // K230 camera control
#include "display_driver.h"     // Display driver
#include "tcm_diagnosis.h"      // TCM diagnosis

/* Interrupt function declarations */
void TIM3_IRQHandler(void) __attribute__((interrupt("WCH-Interrupt-fast")));    // Timer3 interrupt
void USART1_IRQHandler(void) __attribute__((interrupt("WCH-Interrupt-fast")));  // UART1 debug interrupt
void USART3_IRQHandler(void) __attribute__((interrupt("WCH-Interrupt-fast")));  // UART3 control interrupt
void UART6_IRQHandler(void) __attribute__((interrupt("WCH-Interrupt-fast")));   // UART6 K230 interrupt
void UART8_IRQHandler(void) __attribute__((interrupt("WCH-Interrupt-fast")));   // UART8 ESP8266 interrupt
void USART2_IRQHandler(void) __attribute__((interrupt("WCH-Interrupt-fast")));  // UART2 RaspberryPi interrupt

/* Global variables */
unsigned long int uwtick;             // System tick counter

// UART1 receive variables (PC debug communication) - reduced buffer size
u8 uart1_rec_string[64] = {0};
u8 uart1_rec_index = 0;
u8 uart1_rec_tick = 0;

// UART3 receive variables (PC control communication)
u8 uart3_rec_string[128] = {0};
u8 uart3_rec_index = 0;
u8 uart3_rec_tick = 0;

// UART6 receive variables (K230 communication) - reduced buffer size
u8 uart6_rec_string[128] = {0};
u8 uart6_rec_tick;
u8 uart6_rec_index;

// UART8 receive variables (ESP8266 communication) - reduced buffer size
u8 uart8_rec_string[128] = {0};
u8 uart8_rec_index = 0;
u8 uart8_rec_tick = 0;

// UART2 receive variables (RaspberryPi communication) - reduced buffer size
u8 uart2_rec_string[256] = {0};
u8 uart2_rec_index = 0;
u8 uart2_rec_tick = 0;

// System flags
u8 tcm_start_flag = 0;

/* Function prototypes */
void esp8266_proc(void);
void uart8_proc(void);
void uart6_proc(void);
void uart2_proc(void);
void usart1_proc(void);
void uart3_proc(void);
void tcm_main_task(void);

/* Task scheduler */
// #include "scheduler.h"  // Comment out if scheduler.h not available

// Task structure already defined in config.h

// Simple scheduler functions
void scheduler_init(void) {
    // Initialize scheduler
}

// 删除重复的scheduler_run函数和重复的变量定义
// 重复的变量定义已删除，使用文件开头的定义



void esp8266_proc()
{
  if(uart6_rec_index==0)return;
  if(uart6_rec_tick>10)
  {
     usart1_send_string(uart6_rec_string,uart6_rec_index);

     char* add1 = strstr(uart6_rec_string,"appkey");
     int appkey=0;
     sscanf(add1,"appkey\":%d",&appkey);

     char* add2 = strstr(uart6_rec_string,"appstring");
     int appstring=0;
     sscanf(add2,"appstring\":\"%d",&appstring);

    // LCD_ShowIntNum(0, 80,appkey,3, BLUE, GREEN, 16);
    // LCD_ShowIntNum(0, 100,appstring,6, BLUE, GREEN, 16);

     uart6_rec_index=0;
  }

}

void uart8_proc()
{
    if(uart8_rec_index == 0) return; // ??????

    if(uart8_rec_tick > 10) // 10ms??????????????????
    {
        // ????????????????
        // ???????????????: 0x65 0x00 0x04 X_H X_L Y_H Y_L 0xFF 0xFC 0xFF 0xFF
        if(uart8_rec_index >= 10 && uart8_rec_string[0] == 0x65) {
            u16 touch_x = (uart8_rec_string[3] << 8) | uart8_rec_string[4];
            u16 touch_y = (uart8_rec_string[5] << 8) | uart8_rec_string[6];

            // ???????????
            int touch_result = display_handle_touch_event(touch_x, touch_y);
            if(touch_result == 1 && tcm_current_state == TCM_STATE_IDLE) {
                tcm_start_flag = 1; // ????????????
            }
        }
        // ?????????????
        else if(strstr((char*)uart8_rec_string, "touch") != NULL) {
            if(tcm_current_state == TCM_STATE_IDLE) {
                tcm_start_flag = 1;
            }
        }

        // ??????????(???????)
        // display_process_response();

        // ????????
        uart8_rec_index = 0;
        memset(uart8_rec_string, 0, sizeof(uart8_rec_string));
    }
}

// K230??????? (???UART6)
void uart6_proc()
{
    // ????K230???UART6????????????
    if(uart6_rec_index == 0) return;

    if(uart6_rec_tick > 10) {
        // ???K230?????
        if(strstr((char*)uart6_rec_string, "PHOTO_OK") != NULL) {
            printf("K230??????\r\n");
        } else if(strstr((char*)uart6_rec_string, "PHOTO_FAIL") != NULL) {
            printf("K230???????\r\n");
        } else if(strstr((char*)uart6_rec_string, "PHOTO_ERROR") != NULL) {
            printf("K230???????\r\n");
        }

        // ????????
        uart6_rec_index = 0;
        memset(uart6_rec_string, 0, sizeof(uart6_rec_string));
    }
}

// ???????????
void uart2_proc()
{
    if(uart2_rec_index == 0) return;

    if(uart2_rec_tick > 20) {
        // ??????????????????????
        // ??????????JSON?????????
        uart2_rec_index = 0;
        memset(uart2_rec_string, 0, sizeof(uart2_rec_string));
    }
}

// ?????????????(UART3)
void uart3_proc()
{
    if(uart3_rec_index == 0) return;

    if(uart3_rec_tick > 10) {
        printf("UART3???????????: %s\r\n", uart3_rec_string);

        // ????????????
        if(strstr((char*)uart3_rec_string, "K230:") != NULL) {
            // ?????K230
            char* cmd_start = strstr((char*)uart3_rec_string, "K230:") + 5;
            printf("?????K230: %s\r\n", cmd_start);
            uart6_send_string((u8*)cmd_start, strlen(cmd_start));
        }
        else if(strstr((char*)uart3_rec_string, "LED_ON") != NULL) {
            printf("LED????: ????\r\n");
            // ????LED???????
        }
        else if(strstr((char*)uart3_rec_string, "LED_OFF") != NULL) {
            printf("LED????: ???\r\n");
            // ????LED???????
        }
        else if(strstr((char*)uart3_rec_string, "RESET") != NULL) {
            printf("??????????\r\n");
            // ?????????????
        }
        else {
            printf("UART3��?????: %s\r\n", uart3_rec_string);
        }

        // ????????
        uart3_rec_index = 0;
        memset(uart3_rec_string, 0, sizeof(uart3_rec_string));
    }
}

// ????????????????(UART1)
void usart1_proc()
{
    if(uart1_rec_index == 0) return;

    if(uart1_rec_tick > 10) {
        // ??????????
        if(strstr((char*)uart1_rec_string, "photo") != NULL) {
            printf("????????????????K230\r\n");
            u8 photo_cmd[] = "CAPTURE";
            uart6_send_string(photo_cmd, sizeof(photo_cmd)-1);
            printf("?????UART6?????????????K230\r\n");
        }
        else if(strstr((char*)uart1_rec_string, "status") != NULL) {
            printf("???????\r\n");
            printf("UART1: ???????????\r\n");
            printf("UART6: K230???????\r\n");
            printf("UART3: ???????????\r\n");
        }
        else if(strstr((char*)uart1_rec_string, "test") != NULL) {
            printf("??????????\r\n");
            u8 test_cmd[] = "TEST_K230";
            uart6_send_string(test_cmd, sizeof(test_cmd)-1);
        }
        else if(strstr((char*)uart1_rec_string, "help") != NULL) {
            printf("????????:\r\n");
            printf("photo - ????\r\n");
            printf("status - ?????\r\n");
            printf("test - ????K230\r\n");
            printf("send:???? - ??????????????K230\r\n");
        }
        else if(strstr((char*)uart1_rec_string, "send:") != NULL) {
            char* data_start = strstr((char*)uart1_rec_string, "send:") + 5;
            printf("??????????????K230: %s\r\n", data_start);
            uart6_send_string((u8*)data_start, strlen(data_start));
        }
        else {
            printf("��?????: %s\r\n", uart1_rec_string);
            printf("????help??????????\r\n");
        }

        // ????????
        uart1_rec_index = 0;
        memset(uart1_rec_string, 0, sizeof(uart1_rec_string));
    }
}

// ?????????????
void tcm_main_task()
{
    // ??????????
    if(tcm_start_flag) {
        tcm_start_diagnosis();
        tcm_start_flag = 0;
    }

    // ????????????
    tcm_process_task();
}



/*??????????3?????????*/
void TIM3_IRQHandler(void)//?1????
{
    if(TIM_GetITStatus(TIM3, TIM_IT_Update)!=RESET)
    {
      uwtick++;//?????????????
      uart1_rec_tick++; // ????1????????
      uart3_rec_tick++; // UART3???????????
      uart6_rec_tick++;
      uart8_rec_tick++; // ???????????
      uart2_rec_tick++; // ????????????

    }
    TIM_ClearITPendingBit(TIM3, TIM_IT_Update);
}
/*????????????*/
// task_t已在config.h中定义

task_t scheduler_task[]={
        {esp8266_proc,2,0},     // period=2ms, counter=0
        {usart1_proc,1,0},      // period=1ms, counter=0
        {uart3_proc,1,0},       // period=1ms, counter=0
        {uart8_proc,1,0},       // period=1ms, counter=0
        {uart6_proc,1,0},       // period=1ms, counter=0
        {uart2_proc,5,0},       // period=5ms, counter=0
        {tcm_main_task,10,0},   // period=10ms, counter=0
        {ch340_test,1000,0},    // period=1000ms, counter=0
        {ch340_echo_test,1,0},  // period=1ms, counter=0
};
unsigned char task_num;  //????????????
void scheduler_init()
{
    task_num=sizeof(scheduler_task)/sizeof(task_t);  //????????????
}
void scheduler_run()
{
    unsigned char i;
    for(i=0;i<task_num;i++)
    {
        scheduler_task[i].counter++;
        if(scheduler_task[i].counter >= scheduler_task[i].period)
        {
            scheduler_task[i].counter = 0;  // 重置计数器
            scheduler_task[i].task_func();  // 执行任务函数
        }
    }
}

/*??????*/
int main(void)
{
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);//?????????????
	SystemCoreClockUpdate();//???????
	USART_Printf_Init(115200);//????1?????
	Delay_Init();//????????????
    Tim3_Init(1000,96-1);//???????????????


    esp8266_init();
    onenet_init();
    Usart3_Init(); // ��ʼ��UART3���ڵ��Կ���ͨ��
    Uart6_Init(); // ?????UART6??K230???
    Uart8_Init(); // ?????UART8
    ch340_usb_init(); // 初始化CH340 USB转串口测试

    // ??????????
    k230_init();
    display_init();
    tcm_diagnosis_init();

    scheduler_init();//???????????
	while(1)
    {
	    scheduler_run();//??????????
	}
}


/*????6????*/
void UART6_IRQHandler(void)
{
    u8 temp=0;
    if(USART_GetITStatus(UART6, USART_IT_RXNE) != RESET)
       {
        uart6_rec_tick=0;
        temp=USART_ReceiveData(UART6);
        uart6_rec_string[uart6_rec_index]=temp;
        uart6_rec_index++;
       }
    USART_ClearITPendingBit(UART6, USART_IT_RXNE);
}

void UART8_IRQHandler(void)
{
    u8 temp = 0;
    if(USART_GetITStatus(UART8, USART_IT_RXNE) != RESET)
    {
        uart8_rec_tick = 0; // ???????????
        // ????????
        temp = USART_ReceiveData(UART8);
        // ????????
        uart8_rec_string[uart8_rec_index] = temp;
        uart8_rec_index++;

        // ?????????
        USART_ClearITPendingBit(UART8, USART_IT_RXNE);
        // ????????????
        if(uart8_rec_index >= 255)
            uart8_rec_index = 0;
    }
}



// USART1??????????? - ????????
void USART1_IRQHandler(void)
{
    u8 temp = 0;
    if(USART_GetITStatus(USART1, USART_IT_RXNE) != RESET)
    {
        uart1_rec_tick = 0;
        temp = USART_ReceiveData(USART1);

        if(uart1_rec_index < 63) {
            uart1_rec_string[uart1_rec_index] = temp;
            uart1_rec_index++;
        }

        USART_ClearITPendingBit(USART1, USART_IT_RXNE);
    }
}

// USART3?��???????? - ??????????
void USART3_IRQHandler(void)
{
    u8 temp = 0;
    if(USART_GetITStatus(USART3, USART_IT_RXNE) != RESET)
    {
        uart3_rec_tick = 0;
        temp = USART_ReceiveData(USART3);

        if(uart3_rec_index < 127) {
            uart3_rec_string[uart3_rec_index] = temp;
            uart3_rec_index++;
        }

        USART_ClearITPendingBit(USART3, USART_IT_RXNE);
    }
}

// USART2??????????? - ????????
void USART2_IRQHandler(void)
{
    u8 temp = 0;
    if(USART_GetITStatus(USART2, USART_IT_RXNE) != RESET)
    {
        uart2_rec_tick = 0;
        temp = USART_ReceiveData(USART2);

        if(uart2_rec_index < 255) {
            uart2_rec_string[uart2_rec_index] = temp;
            uart2_rec_index++;
        }

        USART_ClearITPendingBit(USART2, USART_IT_RXNE);
    }
}





