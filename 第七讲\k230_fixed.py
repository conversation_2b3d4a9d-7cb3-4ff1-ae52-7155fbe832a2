#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K230 Fixed Version - Compatible with K230 CanMV UART API
No any() method, uses alternative UART reading
"""

import time
import os
from machine import UART

class K230Fixed:
    def __init__(self):
        # UART config - simple initialization
        try:
            self.uart = UART(1, 115200)
            print("UART initialized successfully")
        except Exception as e:
            print(f"UART init failed: {e}")
            self.uart = None
        
        # Photo counter
        self.photo_count = 0
        
        print("K230 Fixed initialized")
        self.send_uart("INIT_OK")
    
    def send_uart(self, msg):
        """Send message to UART"""
        if not self.uart:
            print(f"No UART, would send: {msg}")
            return
        
        try:
            data = f"{msg}\r\n".encode('utf-8')
            self.uart.write(data)
            print(f"Send: {msg}")
        except Exception as e:
            print(f"UART send failed: {e}")
    
    def read_uart_data(self):
        """Read UART data without using any() method"""
        if not self.uart:
            return None
        
        try:
            # Try to read with short timeout
            data = self.uart.read(64)  # Try to read up to 64 bytes
            return data
        except Exception as e:
            # If read fails, return None
            return None
    
    def simulate_photo(self):
        """Simulate taking photo"""
        try:
            filename = f"photo_{self.photo_count:03d}.txt"
            
            with open(filename, 'w') as f:
                f.write(f"Photo simulation {self.photo_count}\n")
                f.write(f"Timestamp: {time.ticks_ms()}\n")
                f.write(f"K230 CanMV test\n")
            
            self.photo_count += 1
            print(f"Photo simulated: {filename}")
            self.send_uart(f"PHOTO_OK:{filename}")
            return True
            
        except Exception as e:
            print(f"Photo simulation failed: {e}")
            self.send_uart("PHOTO_FAIL")
            return False
    
    def process_cmd(self, cmd):
        """Process command"""
        cmd = cmd.strip().lower()
        print(f"Processing: '{cmd}'")
        
        if cmd == "photo":
            self.simulate_photo()
        elif cmd == "test":
            self.send_uart("TEST_OK")
        elif cmd == "ping":
            self.send_uart("PONG")
        elif cmd == "count":
            self.send_uart(f"COUNT:{self.photo_count}")
        elif cmd == "status":
            self.send_uart(f"STATUS_OK:COUNT_{self.photo_count}")
        elif cmd == "reset":
            self.photo_count = 0
            self.send_uart("RESET_OK")
        elif cmd == "hello":
            self.send_uart("HELLO_K230")
        else:
            self.send_uart(f"UNKNOWN:{cmd}")
    
    def main_loop(self):
        """Main program loop - no any() method"""
        print("K230 Fixed Program started")
        self.send_uart("K230_READY")
        
        buffer = ""
        loop_count = 0
        
        while True:
            try:
                # Read UART data without any()
                data = self.read_uart_data()
                
                if data:
                    try:
                        if isinstance(data, bytes):
                            buffer += data.decode('utf-8', errors='ignore')
                        else:
                            buffer += str(data)
                        
                        # Process complete commands
                        while '\n' in buffer:
                            line, buffer = buffer.split('\n', 1)
                            cmd = line.strip('\r\n ')
                            if cmd:
                                print(f"Received: {cmd}")
                                self.process_cmd(cmd)
                    
                    except Exception as e:
                        print(f"Data processing error: {e}")
                
                # Heartbeat every 10 seconds
                loop_count += 1
                if loop_count % 200 == 0:  # 200 * 50ms = 10 seconds
                    print(f"Heartbeat - Photos: {self.photo_count}, Loop: {loop_count}")
                    self.send_uart("HEARTBEAT")
                
                time.sleep_ms(50)
                
            except KeyboardInterrupt:
                print("Program interrupted")
                break
            except Exception as e:
                print(f"Loop error: {e}")
                time.sleep_ms(100)
        
        print("Program ended")

def test_uart_methods():
    """Test different UART methods available"""
    print("=== Testing UART Methods ===")
    
    try:
        uart = UART(1, 115200)
        print("UART created successfully")
        
        # Test available methods
        methods = ['any', 'read', 'write', 'readline', 'readinto']
        for method in methods:
            if hasattr(uart, method):
                print(f"✓ {method} method available")
            else:
                print(f"✗ {method} method NOT available")
        
        # Test write
        try:
            uart.write(b"TEST\r\n")
            print("✓ Write test successful")
        except Exception as e:
            print(f"✗ Write test failed: {e}")
        
        # Test read
        try:
            data = uart.read(1)
            print(f"✓ Read test successful: {data}")
        except Exception as e:
            print(f"✗ Read test failed: {e}")
        
    except Exception as e:
        print(f"UART creation failed: {e}")
    
    print("=== UART Test Complete ===")

def main():
    """Main function"""
    print("=== K230 Fixed Program ===")
    print("Compatible with K230 CanMV UART (no any() method)")
    print("Supported commands:")
    print("  photo  - Simulate photo")
    print("  test   - Test connection")
    print("  ping   - Ping test")
    print("  count  - Query count")
    print("  status - Query status")
    print("  reset  - Reset counter")
    print("  hello  - Hello test")
    print("===============================")
    
    # Optional: Test UART methods first
    # test_uart_methods()
    
    try:
        app = K230Fixed()
        app.main_loop()
    except Exception as e:
        print(f"Program exception: {e}")
        try:
            import sys
            sys.print_exception(e)
        except:
            pass

if __name__ == "__main__":
    main()
