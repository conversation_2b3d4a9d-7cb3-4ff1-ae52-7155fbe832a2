#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K230修复版本 - 解决UART接收问题
"""

import time
import os
from machine import UART

class K230Fixed:
    def __init__(self):
        """初始化K230修复程序"""
        print("=== K230修复程序启动 ===")
        
        # UART初始化
        try:
            self.uart = UART(1, 115200)
            print("✓ UART初始化成功 (115200波特率)")
            print(f"✓ UART对象: {self.uart}")
        except Exception as e:
            print(f"✗ UART初始化失败: {e}")
            self.uart = None
        
        # 计数器
        self.photo_count = 0
        self.test_count = 0
        self.receive_count = 0
        
        print("✓ K230修复程序初始化完成")
        self.send_message("FIXED_INIT_OK")
    
    def send_message(self, msg):
        """发送消息到UART"""
        if not self.uart:
            print(f"[无UART] 应发送: {msg}")
            return
        
        try:
            data = f"{msg}\r\n".encode('utf-8')
            self.uart.write(data)
            print(f"✓ 发送: {msg}")
        except Exception as e:
            print(f"✗ 发送失败: {e}")
    
    def read_uart_improved(self):
        """改进的UART读取方法"""
        if not self.uart:
            return None
        
        try:
            # 方法1: 检查any()方法
            try:
                if hasattr(self.uart, 'any'):
                    available = self.uart.any()
                    if available > 0:
                        data = self.uart.read(available)
                        print(f"✓ any()读取: {data} ({available}字节)")
                        return data
            except:
                pass
            
            # 方法2: 非阻塞读取
            data = self.uart.read(1)
            if data and len(data) > 0:
                print(f"✓ 读取到数据: {data}")
                # 继续读取可能的剩余数据
                time.sleep_ms(5)
                more = self.uart.read(100)
                if more:
                    data += more
                    print(f"✓ 追加数据: {more}")
                return data
            
            # 方法3: 尝试readline
            try:
                line = self.uart.readline()
                if line and len(line) > 0:
                    print(f"✓ readline: {line}")
                    return line
            except:
                pass
            
            return None
            
        except Exception as e:
            print(f"✗ 读取异常: {e}")
            return None
    
    def process_command(self, cmd):
        """处理命令"""
        cmd = cmd.strip().lower()
        print(f"✓ 执行命令: '{cmd}'")
        
        if cmd == "photo":
            self.photo_count += 1
            self.send_message(f"PHOTO_OK:{self.photo_count}")
            
        elif cmd == "test":
            self.test_count += 1
            self.send_message(f"TEST_OK:{self.test_count}")
            
        elif cmd == "ping":
            self.send_message("PONG")
            
        elif cmd == "count":
            self.send_message(f"COUNT:{self.photo_count}")
            
        elif cmd == "status":
            self.send_message(f"STATUS:P{self.photo_count}_T{self.test_count}_R{self.receive_count}")
            
        elif cmd == "reset":
            self.photo_count = 0
            self.test_count = 0
            self.receive_count = 0
            self.send_message("RESET_OK")
            
        else:
            self.send_message(f"UNKNOWN:{cmd}")
    
    def main_loop(self):
        """主循环"""
        print("✓ K230修复程序开始运行")
        self.send_message("FIXED_READY")
        
        buffer = ""
        loop_count = 0
        last_heartbeat = 0
        
        while True:
            try:
                # 读取数据
                data = self.read_uart_improved()
                
                if data:
                    self.receive_count += 1
                    print(f"✓ 第{self.receive_count}次接收")
                    
                    # 处理数据
                    if isinstance(data, bytes):
                        text = data.decode('utf-8', errors='ignore')
                    else:
                        text = str(data)
                    
                    buffer += text
                    print(f"✓ 缓冲区: '{buffer}'")
                    
                    # 处理完整命令
                    while '\n' in buffer or '\r' in buffer:
                        # 处理不同的行结束符
                        if '\n' in buffer:
                            line, buffer = buffer.split('\n', 1)
                        else:
                            line, buffer = buffer.split('\r', 1)
                        
                        command = line.strip('\r\n ')
                        if command:
                            print(f"✓ 提取命令: '{command}'")
                            self.process_command(command)
                
                # 状态显示
                loop_count += 1
                current_time = time.ticks_ms()
                
                # 每10秒显示状态
                if loop_count % 100 == 0:  # 每100次循环
                    print(f"→ 循环{loop_count}, 接收{self.receive_count}次")
                
                # 每20秒心跳
                if current_time - last_heartbeat > 20000:
                    print(f"💓 心跳 - 照片:{self.photo_count} 测试:{self.test_count} 接收:{self.receive_count}")
                    self.send_message("HEARTBEAT")
                    last_heartbeat = current_time
                
                time.sleep_ms(50)  # 50ms延时
                
            except KeyboardInterrupt:
                print("\n程序中断")
                break
            except Exception as e:
                print(f"✗ 主循环错误: {e}")
                time.sleep_ms(100)
        
        print("✓ K230修复程序结束")

def main():
    """主函数"""
    print("=" * 50)
    print("    K230 修复版本 (UTF-8)")
    print("=" * 50)
    print("改进功能:")
    print("  - 多种UART读取方法")
    print("  - 改进的数据处理")
    print("  - 更好的错误处理")
    print("=" * 50)
    print("支持命令:")
    print("  photo, test, ping, count, status, reset")
    print("=" * 50)
    
    try:
        app = K230Fixed()
        app.main_loop()
    except Exception as e:
        print(f"程序异常: {e}")

if __name__ == "__main__":
    main()
