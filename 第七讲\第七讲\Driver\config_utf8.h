/*
 * Configuration Header File - UTF-8 Version
 * CH32V307VCT6 Multi-UART Communication System
 * All configurations and constants
 */

#ifndef __CONFIG_UTF8_H
#define __CONFIG_UTF8_H

#include "ch32v30x.h"

/* System Configuration */
#define SYSTEM_CLOCK_FREQ       96000000    // 96MHz system clock
#define TIMER3_FREQ             1000        // 1ms timer interrupt
#define UART_BAUDRATE           115200      // Standard UART baudrate

/* Buffer Size Configuration */
#define UART1_BUFFER_SIZE       64          // PC debug communication buffer
#define UART2_BUFFER_SIZE       256         // RaspberryPi communication buffer  
#define UART3_BUFFER_SIZE       128         // PC control communication buffer
#define UART6_BUFFER_SIZE       128         // K230 communication buffer
#define UART8_BUFFER_SIZE       128         // ESP8266 communication buffer

/* TCM Diagnosis Configuration */
#define TCM_IMAGE_BUFFER_SIZE   4096        // Reduced to 4KB for memory optimization
#define TCM_DIAGNOSIS_TIMEOUT_MS 5000       // 5 second timeout
#define MAX_PACKET_SIZE         256         // Maximum packet size

/* Communication Protocol Configuration */
#define CMD_PHOTO               "photo"
#define CMD_STATUS              "status"
#define CMD_TEST                "test"
#define CMD_HELP                "help"
#define CMD_SEND_PREFIX         "send:"
#define CMD_K230_PREFIX         "K230:"
#define CMD_LED_ON              "LED_ON"
#define CMD_LED_OFF             "LED_OFF"
#define CMD_RESET               "RESET"

/* K230 Communication Commands */
#define K230_CMD_CAPTURE        "CAPTURE"
#define K230_CMD_TEST           "TEST_K230"
#define K230_RESP_OK            "PHOTO_OK"
#define K230_RESP_FAIL          "PHOTO_FAIL"
#define K230_RESP_ERROR         "PHOTO_ERROR"

/* ESP8266 Configuration */
#define ESP8266_SSID            "YourWiFiSSID"
#define ESP8266_PASSWORD        "YourWiFiPassword"
#define ONENET_DEVICE_ID        "YourDeviceID"
#define ONENET_API_KEY          "YourAPIKey"

/* GPIO Pin Definitions */
// UART1 - Debug communication (PA9/PA10)
#define UART1_TX_PIN            GPIO_Pin_9
#define UART1_RX_PIN            GPIO_Pin_10
#define UART1_GPIO_PORT         GPIOA

// USART2 - RaspberryPi communication (PA2/PA3)
#define UART2_TX_PIN            GPIO_Pin_2
#define UART2_RX_PIN            GPIO_Pin_3
#define UART2_GPIO_PORT         GPIOA

// USART3 - Control communication (PB10/PB11)
#define UART3_TX_PIN            GPIO_Pin_10
#define UART3_RX_PIN            GPIO_Pin_11
#define UART3_GPIO_PORT         GPIOB

// UART6 - K230 communication (PC0/PC1)
#define UART6_TX_PIN            GPIO_Pin_0
#define UART6_RX_PIN            GPIO_Pin_1
#define UART6_GPIO_PORT         GPIOC

// UART8 - ESP8266 communication (PE0/PE1)
#define UART8_TX_PIN            GPIO_Pin_0
#define UART8_RX_PIN            GPIO_Pin_1
#define UART8_GPIO_PORT         GPIOE

/* LED Control Pins */
#define LED1_PIN                GPIO_Pin_0
#define LED1_GPIO_PORT          GPIOA
#define LED2_PIN                GPIO_Pin_1
#define LED2_GPIO_PORT          GPIOA

/* Error Codes */
#define ERR_OK                  0
#define ERR_TIMEOUT             -1
#define ERR_INVALID_PARAM       -2
#define ERR_BUFFER_FULL         -3
#define ERR_COMMUNICATION       -4
#define ERR_DEVICE_NOT_READY    -5

/* Task Scheduler Configuration */
#define MAX_TASKS               10
#define SCHEDULER_TICK_MS       1

/* Debug Configuration */
#ifdef DEBUG
    #define DEBUG_PRINT(fmt, ...) printf(fmt, ##__VA_ARGS__)
#else
    #define DEBUG_PRINT(fmt, ...)
#endif

/* Function Macros */
#define ENABLE_UART_RX_INT(uart)    USART_ITConfig(uart, USART_IT_RXNE, ENABLE)
#define DISABLE_UART_RX_INT(uart)   USART_ITConfig(uart, USART_IT_RXNE, DISABLE)
#define CLEAR_UART_FLAG(uart)       USART_ClearFlag(uart, USART_FLAG_TC)

/* Memory Management */
#define SAFE_MEMSET(ptr, val, size) do { \
    if(ptr != NULL) memset(ptr, val, size); \
} while(0)

#define SAFE_STRLEN(str) (str ? strlen(str) : 0)

/* Communication Timeout Configuration */
#define UART_RX_TIMEOUT_MS      10      // UART receive timeout
#define K230_RESPONSE_TIMEOUT   3000    // K230 response timeout
#define ESP8266_AT_TIMEOUT      5000    // ESP8266 AT command timeout

/* System Status Flags */
typedef enum {
    SYS_STATUS_INIT = 0,
    SYS_STATUS_READY,
    SYS_STATUS_RUNNING,
    SYS_STATUS_ERROR,
    SYS_STATUS_RESET
} system_status_t;

typedef enum {
    UART_STATUS_IDLE = 0,
    UART_STATUS_RECEIVING,
    UART_STATUS_PROCESSING,
    UART_STATUS_ERROR
} uart_status_t;

/* TCM Diagnosis States */
typedef enum {
    TCM_STATE_IDLE = 0,
    TCM_STATE_CAPTURING,
    TCM_STATE_PROCESSING,
    TCM_STATE_DISPLAYING,
    TCM_STATE_ERROR
} tcm_state_t;

/* K230 Camera States */
typedef enum {
    K230_STATE_IDLE = 0,
    K230_STATE_READY,
    K230_STATE_CAPTURING,
    K230_STATE_PROCESSING,
    K230_STATE_ERROR
} k230_state_t;

/* Communication Packet Structure */
typedef struct {
    u8 header[2];       // Packet header
    u8 cmd;             // Command byte
    u16 length;         // Data length
    u8 data[MAX_PACKET_SIZE];  // Data payload
    u8 checksum;        // Checksum
    u8 footer[2];       // Packet footer
} comm_packet_t;

/* Task Structure */
typedef struct {
    void (*task_func)(void);    // Task function pointer
    u16 period;                 // Task period in ms
    u16 counter;                // Task counter
} task_t;

/* Global Configuration Structure */
typedef struct {
    system_status_t system_status;
    uart_status_t uart1_status;
    uart_status_t uart2_status;
    uart_status_t uart3_status;
    uart_status_t uart6_status;
    uart_status_t uart8_status;
    tcm_state_t tcm_state;
    k230_state_t k230_state;
    u32 system_uptime;
    u32 error_count;
} system_config_t;

/* External Variables */
extern system_config_t g_system_config;
extern unsigned long int uwtick;

/* Utility Macros */
#define ARRAY_SIZE(arr)         (sizeof(arr) / sizeof((arr)[0]))
#define MIN(a, b)               ((a) < (b) ? (a) : (b))
#define MAX(a, b)               ((a) > (b) ? (a) : (b))
#define CLAMP(val, min, max)    (MIN(MAX(val, min), max))

/* Version Information */
#define FIRMWARE_VERSION_MAJOR  1
#define FIRMWARE_VERSION_MINOR  0
#define FIRMWARE_VERSION_PATCH  0
#define FIRMWARE_BUILD_DATE     __DATE__
#define FIRMWARE_BUILD_TIME     __TIME__

#endif /* __CONFIG_UTF8_H */
