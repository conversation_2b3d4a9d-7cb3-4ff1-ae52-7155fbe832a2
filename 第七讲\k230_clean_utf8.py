#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K230 Main Program - Clean UTF-8 Version
Compatible with K230 CanMV environment, no any() method dependency
Supports communication with CH32V307 UART6
"""

import time
import os
from machine import UART

class K230Main:
    def __init__(self):
        """Initialize K230 program"""
        print("=== K230 Main Program Start ===")
        
        # UART init - corresponds to CH32V307 UART6(PC0/PC1)
        try:
            self.uart = UART(1, 115200)
            print("UART init success (115200 baud)")
        except Exception as e:
            print(f"UART init failed: {e}")
            self.uart = None
        
        # Counters
        self.photo_count = 0
        self.test_count = 0
        
        print("K230 program init complete")
        self.send_message("INIT_OK")
    
    def send_message(self, msg):
        """Send message to UART"""
        if not self.uart:
            print(f"[No UART] Would send: {msg}")
            return
        
        try:
            data = f"{msg}\r\n".encode('utf-8')
            self.uart.write(data)
            print(f"Send: {msg}")
        except Exception as e:
            print(f"Send failed: {e}")
    
    def read_uart_data(self):
        """Read UART data - no any() method"""
        if not self.uart:
            return None
        
        try:
            # Direct read without any() check
            data = self.uart.read(128)  # Read up to 128 bytes
            return data
        except Exception as e:
            return None
    
    def create_test_photo(self):
        """Create test photo file"""
        try:
            timestamp = time.ticks_ms()
            filename = f"photo_{self.photo_count:04d}_{timestamp}.txt"
            
            # Create test file
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"K230 Test Photo #{self.photo_count}\n")
                f.write(f"Timestamp: {timestamp}\n")
                f.write(f"Filename: {filename}\n")
                f.write(f"UART Communication Test Success\n")
                f.write(f"Encoding: UTF-8\n")
            
            self.photo_count += 1
            print(f"Test photo created: {filename}")
            self.send_message(f"PHOTO_OK:{filename}")
            return True
            
        except Exception as e:
            print(f"Photo creation failed: {e}")
            self.send_message("PHOTO_FAIL")
            return False
    
    def process_command(self, cmd):
        """Process received command"""
        cmd = cmd.strip().lower()
        print(f"Process command: '{cmd}'")
        
        if cmd == "photo":
            self.create_test_photo()
            
        elif cmd == "test":
            self.test_count += 1
            self.send_message(f"TEST_OK:{self.test_count}")
            
        elif cmd == "ping":
            self.send_message("PONG")
            
        elif cmd == "count":
            self.send_message(f"COUNT:{self.photo_count}")
            
        elif cmd == "status":
            self.send_message(f"STATUS_OK:PHOTOS_{self.photo_count}_TESTS_{self.test_count}")
            
        elif cmd == "reset":
            self.photo_count = 0
            self.test_count = 0
            self.send_message("RESET_OK")
            
        elif cmd == "hello":
            self.send_message("HELLO_K230_UTF8")
            
        elif cmd == "info":
            self.send_message(f"INFO:K230_CANMV_UTF8_PHOTOS_{self.photo_count}")
            
        else:
            self.send_message(f"UNKNOWN:{cmd}")
    
    def main_loop(self):
        """Main loop"""
        print("K230 main program running")
        self.send_message("K230_READY")
        
        buffer = ""
        loop_count = 0
        last_heartbeat = 0
        
        while True:
            try:
                # Read UART data
                data = self.read_uart_data()
                
                if data:
                    try:
                        # Process received data
                        if isinstance(data, bytes):
                            text = data.decode('utf-8', errors='ignore')
                        else:
                            text = str(data)
                        
                        buffer += text
                        
                        # Process complete command lines
                        while '\n' in buffer:
                            line, buffer = buffer.split('\n', 1)
                            command = line.strip('\r\n ')
                            
                            if command:
                                print(f"Received: {command}")
                                self.process_command(command)
                    
                    except Exception as e:
                        print(f"Data processing error: {e}")
                
                # Heartbeat and status display
                loop_count += 1
                current_time = time.ticks_ms()
                
                # Send heartbeat every 10 seconds
                if current_time - last_heartbeat > 10000:
                    print(f"Heartbeat - Photos: {self.photo_count}, Tests: {self.test_count}, Loop: {loop_count}")
                    self.send_message("HEARTBEAT")
                    last_heartbeat = current_time
                
                time.sleep_ms(50)  # 50ms delay
                
            except KeyboardInterrupt:
                print("\nProgram interrupted by user")
                break
            except Exception as e:
                print(f"Main loop error: {e}")
                time.sleep_ms(100)
        
        print("K230 program ended")

def main():
    """Main function"""
    print("=" * 50)
    print("    K230 CanMV Main Program (Clean UTF-8)")
    print("=" * 50)
    print("Supported commands:")
    print("  photo  - Create test photo file")
    print("  test   - Test connection")
    print("  ping   - Ping test")
    print("  count  - Query photo count")
    print("  status - Query status")
    print("  reset  - Reset counters")
    print("  hello  - Hello test")
    print("  info   - System info")
    print("=" * 50)
    
    try:
        app = K230Main()
        app.main_loop()
    except Exception as e:
        print(f"Program exception: {e}")
        try:
            import sys
            sys.print_exception(e)
        except:
            pass

if __name__ == "__main__":
    main()
