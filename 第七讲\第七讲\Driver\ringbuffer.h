// ringbuffer.h
#ifndef RINGBUFFER_H
#define RINGBUFFER_H

#include "debug.h"

typedef struct {
    uint8_t *buffer;
    uint16_t head;
    uint16_t tail;
    uint16_t size;
    uint16_t capacity;
} RingBuffer;

void RingBuffer_Init(RingBuffer *rb, uint8_t *pool, uint16_t size);
uint16_t RingBuffer_Put(RingBuffer *rb, const uint8_t *data, uint16_t len);
uint16_t RingBuffer_Get(RingBuffer *rb, uint8_t *data, uint16_t len);
uint16_t RingBuffer_Available(RingBuffer *rb);

#endif
