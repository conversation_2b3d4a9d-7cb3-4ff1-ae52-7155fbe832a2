#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K230拍照测试脚本
通过串口发送命令测试K230拍照功能
"""

import serial
import time
import sys

class K230Tester:
    def __init__(self, port='COM3', baudrate=115200):
        """初始化串口连接"""
        try:
            self.ser = serial.Serial(port, baudrate, timeout=1)
            print(f"串口 {port} 连接成功，波特率: {baudrate}")
        except Exception as e:
            print(f"串口连接失败: {e}")
            sys.exit(1)
    
    def send_command(self, cmd):
        """发送命令到K230"""
        try:
            command = f"{cmd}\r\n"
            self.ser.write(command.encode())
            print(f"发送命令: {cmd}")
            
            # 等待响应
            time.sleep(0.5)
            if self.ser.in_waiting > 0:
                response = self.ser.read(self.ser.in_waiting).decode('utf-8', errors='ignore')
                print(f"收到响应: {response.strip()}")
                return response
            else:
                print("无响应")
                return None
                
        except Exception as e:
            print(f"发送命令失败: {e}")
            return None
    
    def test_photo(self):
        """测试拍照功能"""
        print("\n=== 拍照测试 ===")
        response = self.send_command("photo")
        
        if response and "PHOTO_OK" in response:
            print("? 拍照成功!")
        elif response and "PHOTO_FAIL" in response:
            print("? 拍照失败!")
        else:
            print("? 未知响应")
    
    def test_connection(self):
        """测试连接"""
        print("\n=== 连接测试 ===")
        response = self.send_command("test")
        
        if response and "TEST_OK" in response:
            print("? 连接正常!")
        else:
            print("? 连接异常!")
    
    def get_status(self):
        """获取状态"""
        print("\n=== 状态查询 ===")
        response = self.send_command("count")
        
        if response:
            print(f"状态: {response.strip()}")
        else:
            print("无状态响应")
    
    def interactive_mode(self):
        """交互模式"""
        print("\n=== 交互模式 ===")
        print("可用命令: photo, test, count, quit")
        print("输入命令后按回车发送")
        
        while True:
            try:
                cmd = input("\n请输入命令: ").strip()
                
                if cmd.lower() == 'quit':
                    break
                elif cmd.lower() == 'help':
                    print("可用命令:")
                    print("  photo - 拍照")
                    print("  test  - 测试连接")
                    print("  count - 查询拍照数量")
                    print("  quit  - 退出")
                elif cmd:
                    self.send_command(cmd)
                    
            except KeyboardInterrupt:
                break
    
    def auto_test(self):
        """自动测试序列"""
        print("\n=== 自动测试序列 ===")
        
        # 1. 连接测试
        self.test_connection()
        time.sleep(1)
        
        # 2. 状态查询
        self.get_status()
        time.sleep(1)
        
        # 3. 拍照测试
        for i in range(3):
            print(f"\n第 {i+1} 次拍照:")
            self.test_photo()
            time.sleep(2)
        
        # 4. 最终状态
        self.get_status()
    
    def close(self):
        """关闭串口"""
        if hasattr(self, 'ser') and self.ser.is_open:
            self.ser.close()
            print("串口已关闭")

def main():
    """主函数"""
    print("=== K230拍照测试工具 ===")
    
    # 配置串口参数
    port = input("请输入串口号 (默认COM3): ").strip() or "COM3"
    
    try:
        tester = K230Tester(port)
        
        while True:
            print("\n请选择测试模式:")
            print("1. 自动测试")
            print("2. 交互模式")
            print("3. 单次拍照")
            print("4. 连接测试")
            print("5. 退出")
            
            choice = input("请选择 (1-5): ").strip()
            
            if choice == '1':
                tester.auto_test()
            elif choice == '2':
                tester.interactive_mode()
            elif choice == '3':
                tester.test_photo()
            elif choice == '4':
                tester.test_connection()
            elif choice == '5':
                break
            else:
                print("无效选择，请重新输入")
    
    except Exception as e:
        print(f"程序异常: {e}")
    
    finally:
        if 'tester' in locals():
            tester.close()

if __name__ == "__main__":
    main()
