
#ifndef USART_H
#define USART_H

#include "ringbuffer.h"
#include "debug.h"

#define RX_BUFF_LENGTH      64
#define TX_BUFF_LENGTH      64

typedef struct {
    uint8_t send_buff[TX_BUFF_LENGTH];
    uint8_t rec_buff[RX_BUFF_LENGTH];
    volatile uint16_t rx_count;
} UART_DMA_Buff;

extern UART_DMA_Buff uart_dma_rx_buff;

void UART7_DMA_Init(uint32_t baudrate);
void UART_SendData(uint8_t *data, uint16_t length);

void Usart3_Init();//???????????
void Usart2_Init();//??????????
void usart1_send_string(u8* string,u8 len);

void Uart6_Init();
void uart6_send_string(u8* string,u8 len);

void Uart8_Init();
void uart8_send_string(u8* string,u8 len);

int my_printf(USART_TypeDef* USARTx, const char *format, ...);

#endif
