#ifndef __UART_H
#define __UART_H

#include "ch32v30x.h"
#include "config.h"
#include <stdarg.h>

/* UART????????? */
void Usart2_Init(void);     // ???????????
void Usart3_Init(void);     // PC??????????  
void Uart6_Init(void);      // K230??????
void Uart8_Init(void);      // ESP8266 WiFi??????

/* UART??????? */
void usart1_send_string(u8* string, u8 len);   // ???????
void usart2_send_string(u8* string, u8 len);   // ????????
void usart3_send_string(u8* string, u8 len);   // ???????
void uart6_send_string(u8* string, u8 len);    // K230???
void uart8_send_string(u8* string, u8 len);    // ESP8266???

/* ??????? */
void uart6_printf(const char* format, ...);    // ??????????K230
void uart8_send_at_command(const char* cmd);   // ????AT????ESP8266
u8 uart_data_available(USART_TypeDef* uart);   // ????????????
void uart_flush_rx_buffer(USART_TypeDef* uart); // ???????????



#endif /* __UART_H */
