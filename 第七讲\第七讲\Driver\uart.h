/*
 * UART Driver Header - UTF-8 Version
 * CH32V307VCT6 Multi-UART Communication
 * Function declarations and definitions
 */

#ifndef __UART_UTF8_H
#define __UART_UTF8_H

#include "ch32v30x.h"
#include "config.h"
#include <stdarg.h>

/* UART Initialization Functions */
void Usart2_Init(void);     // RaspberryPi communication module
void Usart3_Init(void);     // PC control communication module  
void Uart6_Init(void);      // K230 communication module
void Uart8_Init(void);      // ESP8266 WiFi communication module

/* UART Send Functions */
void usart1_send_string(u8* string, u8 len);   // Debug output
void usart2_send_string(u8* string, u8 len);   // RaspberryPi communication
void usart3_send_string(u8* string, u8 len);   // Control communication
void uart6_send_string(u8* string, u8 len);    // K230 communication
void uart8_send_string(u8* string, u8 len);    // ESP8266 communication

/* Utility Functions */
void uart6_printf(const char* format, ...);    // Formatted output to K230
void uart8_send_at_command(const char* cmd);   // Send AT command to ESP8266
u8 uart_data_available(USART_TypeDef* uart);   // Check data availability
void uart_flush_rx_buffer(USART_TypeDef* uart); // Flush receive buffer

/* UART Status Check Macros */
#define UART1_DATA_READY()      uart_data_available(USART1)
#define UART2_DATA_READY()      uart_data_available(USART2)
#define UART3_DATA_READY()      uart_data_available(USART3)
#define UART6_DATA_READY()      uart_data_available(UART6)
#define UART8_DATA_READY()      uart_data_available(UART8)

/* UART Buffer Flush Macros */
#define FLUSH_UART1_RX()        uart_flush_rx_buffer(USART1)
#define FLUSH_UART2_RX()        uart_flush_rx_buffer(USART2)
#define FLUSH_UART3_RX()        uart_flush_rx_buffer(USART3)
#define FLUSH_UART6_RX()        uart_flush_rx_buffer(UART6)
#define FLUSH_UART8_RX()        uart_flush_rx_buffer(UART8)

#endif /* __UART_UTF8_H */
