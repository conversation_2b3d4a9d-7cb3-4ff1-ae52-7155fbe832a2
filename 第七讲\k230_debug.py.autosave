#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K230调试版本 - 诊断UART通信问题
"""

import time
import os
from machine import UART

class K230Debug:
    def __init__(self):
        """初始化K230调试程序"""
        print("=== K230调试程序启动 ===")

        # UART初始化 - 对应CH32V307的UART6(PC0/PC1)
        try:
            self.uart = UART(1, 115200)
            print(" UART初始化成功 (115200波特率)")
            print(f" UART对象: {self.uart}")
        except Exception as e:
            print(f" UART初始化失败: {e}")
            self.uart = None

        # 计数器
        self.photo_count = 0
        self.test_count = 0
        self.receive_count = 0

        print(" K230调试程序初始化完成")
        self.send_message("DEBUG_INIT_OK")

    def send_message(self, msg):
        """发送消息到UART"""
        if not self.uart:
            print(f"[无UART] 应发送: {msg}")
            return

        try:
            data = f"{msg}\r\n".encode('utf-8')
            self.uart.write(data)
            print(f" 发送成功: {msg} (字节数: {len(data)})")
        except Exception as e:
            print(f" 发送失败: {e}")

    def read_uart_data_debug(self):
        """调试版UART数据读取"""
        if not self.uart:
            return None

        try:
            # 尝试多种读取方法
            print("→ 尝试读取UART数据...")

            # 方法1: 直接读取
            data1 = self.uart.read(1)
            if data1:
                print(f" 方法1读取到: {data1} (类型: {type(data1)})")
                return data1

            # 方法2: 读取更多字节
            data2 = self.uart.read(128)
            if data2:
                print(f" 方法2读取到: {data2} (类型: {type(data2)})")
                return data2

            # 方法3: 尝试readline
            try:
                data3 = self.uart.readline()
                if data3:
                    print(f" 方法3读取到: {data3} (类型: {type(data3)})")
                    return data3
            except:
                pass

            return None

        except Exception as e:
            print(f" 读取异常: {e}")
            return None

    def process_command(self, cmd):
        """处理接收到的命令"""
        cmd = cmd.strip().lower()
        print(f" 处理命令: '{cmd}'")

        if cmd == "photo":
            self.photo_count += 1
            print(f" 照片命令执行 (总数: {self.photo_count})")
            self.send_message(f"PHOTO_OK:{self.photo_count}")

        elif cmd == "test":
            self.test_count += 1
            print(f" 测试命令执行 (总数: {self.test_count})")
            self.send_message(f"TEST_OK:{self.test_count}")

        elif cmd == "ping":
            print(" Ping命令执行")
            self.send_message("PONG")

        elif cmd == "debug":
            print(" 调试信息:")
            print(f"  - 照片数: {self.photo_count}")
            print(f"  - 测试数: {self.test_count}")
            print(f"  - 接收数: {self.receive_count}")
            print(f"  - UART状态: {self.uart}")
            self.send_message(f"DEBUG_INFO:P{self.photo_count}_T{self.test_count}_R{self.receive_count}")

        else:
            print(f" 未知命令: {cmd}")
            self.send_message(f"UNKNOWN:{cmd}")

    def main_loop(self):
        """主循环 - 调试版本"""
        print(" K230调试程序开始运行")
        self.send_message("DEBUG_READY")

        buffer = ""
        loop_count = 0
        last_heartbeat = 0
        last_status = 0

        while True:
            try:
                # 读取UART数据
                data = self.read_uart_data_debug()

                if data:
                    self.receive_count += 1
                    print(f" 第{self.receive_count}次接收到数据")

                    try:
                        # 处理接收到的数据
                        if isinstance(data, bytes):
                            text = data.decode('utf-8', errors='ignore')
                        else:
                            text = str(data)

                        print(f" 解码后文本: '{text}' (长度: {len(text)})")
                        buffer += text

                        # 处理完整的命令行
                        while '\n' in buffer:
                            line, buffer = buffer.split('\n', 1)
                            command = line.strip('\r\n ')

                            if command:
                                print(f" 提取命令: '{command}'")
                                self.process_command(command)

                    except Exception as e:
                        print(f" 数据处理错误: {e}")

                # 状态显示和心跳
                loop_count += 1
                current_time = time.ticks_ms()

                # 每5秒显示状态
                if current_time - last_status > 5000:
                    print(f"→ 状态: 循环{loop_count}, 接收{self.receive_count}次, 照片{self.photo_count}, 测试{self.test_count}")
                    last_status = current_time

                # 每15秒发送心跳
                if current_time - last_heartbeat > 15000:
                    print(f" 心跳发送")
                    self.send_message("DEBUG_HEARTBEAT")
                    last_heartbeat = current_time

                time.sleep_ms(100)  # 100ms延时，更频繁检查

            except KeyboardInterrupt:
                print("\n程序被用户中断")
                break
            except Exception as e:
                print(f" 主循环错误: {e}")
                time.sleep_ms(200)

        print(" K230调试程序结束")

def main():
    """主函数"""
    print("=" * 60)
    print("    K230 CanMV 调试程序 (UTF-8编码)")
    print("=" * 60)
    print("调试功能:")
    print("  - 详细的UART读取日志")
    print("  - 多种数据读取方法测试")
    print("  - 实时状态显示")
    print("  - 增强的错误处理")
    print("=" * 60)
    print("支持命令:")
    print("  photo  - 创建照片")
    print("  test   - 测试连接")
    print("  ping   - Ping测试")
    print("  debug  - 显示调试信息")
    print("=" * 60)

    try:
        app = K230Debug()
        app.main_loop()
    except Exception as e:
        print(f"程序异常: {e}")
        try:
            import sys
            sys.print_exception(e)
        except:
            pass

if __name__ == "__main__":
    main()
