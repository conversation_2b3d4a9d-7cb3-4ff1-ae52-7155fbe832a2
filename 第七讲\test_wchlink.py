#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WCH-Link连接K230测试工具
"""

import serial
import time
import sys

def find_wchlink_port():
    """查找WCH-Link串口"""
    try:
        import serial.tools.list_ports
        ports = serial.tools.list_ports.comports()
        
        print("可用串口:")
        for i, port in enumerate(ports):
            print(f"  {i+1}. {port.device} - {port.description}")
            if 'WCH' in port.description.upper() or 'CH340' in port.description.upper():
                print(f"     ← 可能是WCH-Link")
        
        return None
    except:
        return None

def test_k230_wchlink(port):
    """测试K230通过WCH-Link"""
    print(f"=== WCH-Link K230 测试 ===")
    print(f"连接端口: {port}")
    
    try:
        # 打开串口
        ser = serial.Serial(port, 115200, timeout=1)
        print(f"✓ 连接成功")
        
        # 清空缓冲区
        ser.flushInput()
        ser.flushOutput()
        time.sleep(1)
        
        print("\n开始测试...")
        
        # 测试命令
        commands = ["ping", "test", "photo", "count", "status"]
        
        for cmd in commands:
            print(f"\n发送命令: {cmd}")
            
            # 发送命令
            ser.write(f"{cmd}\r\n".encode('utf-8'))
            
            # 等待响应
            start_time = time.time()
            while time.time() - start_time < 2:
                if ser.in_waiting > 0:
                    response = ser.read(ser.in_waiting).decode('utf-8', errors='ignore')
                    print(f"收到: {repr(response)}")
                    break
                time.sleep(0.1)
            else:
                print("无响应")
            
            time.sleep(0.5)
        
        # 监听模式
        print(f"\n监听K230输出 (5秒)...")
        start_time = time.time()
        while time.time() - start_time < 5:
            if ser.in_waiting > 0:
                data = ser.read(ser.in_waiting).decode('utf-8', errors='ignore')
                print(f"监听: {repr(data)}")
            time.sleep(0.2)
        
        ser.close()
        print("\n测试完成")
        
    except Exception as e:
        print(f"错误: {e}")

def interactive_test(port):
    """交互测试"""
    try:
        ser = serial.Serial(port, 115200, timeout=1)
        print(f"连接到 {port}")
        print("输入命令测试 (quit退出):")
        
        while True:
            cmd = input("命令> ").strip()
            if cmd.lower() == 'quit':
                break
            
            if cmd:
                ser.write(f"{cmd}\r\n".encode('utf-8'))
                time.sleep(0.3)
                
                if ser.in_waiting > 0:
                    response = ser.read(ser.in_waiting).decode('utf-8', errors='ignore')
                    print(f"响应: {repr(response)}")
                else:
                    print("无响应")
        
        ser.close()
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    print("=== WCH-Link K230 测试工具 ===")
    
    # 查找端口
    find_wchlink_port()
    
    # 获取端口
    if len(sys.argv) > 1:
        port = sys.argv[1]
    else:
        port = input("\n请输入串口号 (如COM3): ").strip()
        if not port:
            port = "COM3"
    
    print(f"\n选择测试模式:")
    print("1. 自动测试")
    print("2. 交互测试")
    
    choice = input("选择 (1/2): ").strip()
    
    if choice == '2':
        interactive_test(port)
    else:
        test_k230_wchlink(port)
