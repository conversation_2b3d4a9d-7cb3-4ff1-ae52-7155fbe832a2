#include "k230_camera.h"
#include "uart.h"
#include "string.h"
#include "debug.h"

// ??????????
extern unsigned long int uwtick;

// ??????
k230_state_t k230_state = K230_STATE_IDLE;
k230_image_t current_image = {0};
static u8 k230_rx_buffer[MAX_PACKET_SIZE];
static u16 k230_rx_index = 0;
static u8 k230_rx_complete = 0;
static u8 k230_image_buffer[TCM_IMAGE_BUFFER_SIZE]; // ????????????
static u32 k230_image_received_size = 0;
static u32 k230_expected_image_size = 0;
static u8 k230_receiving_image = 0;

void k230_init(void) {
    // UART6????main?????????????????????????
    k230_state = K230_STATE_IDLE;
    memset(&current_image, 0, sizeof(k230_image_t));
    k230_rx_index = 0;
    k230_rx_complete = 0;
    k230_image_received_size = 0;
    k230_expected_image_size = 0;
    k230_receiving_image = 0;
    memset(k230_image_buffer, 0, sizeof(k230_image_buffer));
}

// ?????????
void k230_reset_receive_state(void) {
    k230_rx_index = 0;
    k230_rx_complete = 0;
    k230_image_received_size = 0;
    k230_expected_image_size = 0;
    k230_receiving_image = 0;
    memset(k230_rx_buffer, 0, sizeof(k230_rx_buffer));
}

int k230_capture_image(k230_image_t* img) {
    if (k230_state != K230_STATE_IDLE) return ERR_COMM_FAIL;

    // ?????????
    k230_reset_receive_state();
    k230_state = K230_STATE_CAPTURING;

    // ??????????????????????
    u8 cmd_data[6] = {
        K230_IMG_WIDTH >> 8, K230_IMG_WIDTH & 0xFF,
        K230_IMG_HEIGHT >> 8, K230_IMG_HEIGHT & 0xFF,
        K230_IMG_FORMAT_JPEG, 0x80  // ?????????(0x80=??????)
    };

    int ret = k230_send_command(K230_CMD_CAPTURE, cmd_data, 6);
    if (ret != ERR_OK) {
        k230_state = K230_STATE_ERROR;
        return ret;
    }

    // ???K230??????????????????
    u32 timeout = uwtick + TCM_DIAGNOSIS_TIMEOUT_MS;
    while (k230_state == K230_STATE_CAPTURING && uwtick < timeout) {
        k230_process_data();
        Delay_Ms(5); // ????CPU???
    }

    if (k230_state == K230_STATE_CAPTURING) {
        k230_state = K230_STATE_ERROR;
        return ERR_TIMEOUT;
    }

    if (k230_state == K230_STATE_ERROR) {
        return ERR_COMM_FAIL;
    }

    // ???????????????????
    if (img && current_image.data && current_image.size > 0) {
        img->width = current_image.width;
        img->height = current_image.height;
        img->format = current_image.format;
        img->size = current_image.size;
        img->data = current_image.data; // ????????????
        return ERR_OK;
    }

    return ERR_COMM_FAIL;
}

int k230_send_command(u8 cmd, u8* data, u16 len) {
    k230_packet_t packet;
    packet.header = PROTOCOL_HEADER;
    packet.cmd = cmd;
    packet.length = len;
    
    if (data && len > 0) {
        memcpy(packet.data, data, len);
    }
    
    packet.checksum = k230_calculate_checksum((u8*)&packet, sizeof(packet) - 2);
    packet.footer = PROTOCOL_FOOTER;
    
    uart6_send_string((u8*)&packet, sizeof(packet.header) + sizeof(packet.cmd) +
                     sizeof(packet.length) + len + sizeof(packet.checksum) + sizeof(packet.footer));
    
    return ERR_OK;
}

void k230_process_data(void) {
    if (k230_rx_complete) {
        k230_packet_t* packet = (k230_packet_t*)k230_rx_buffer;

        if (packet->header == PROTOCOL_HEADER && packet->footer == PROTOCOL_FOOTER) {
            u8 calc_checksum = k230_calculate_checksum(k230_rx_buffer, k230_rx_index - 2);
            if (calc_checksum == packet->checksum) {

                if (packet->cmd == K230_CMD_CAPTURE) {
                    if (!k230_receiving_image) {
                        // ??????????????????????
                        if (packet->length >= 4) {
                            k230_expected_image_size = (packet->data[0] << 24) |
                                                     (packet->data[1] << 16) |
                                                     (packet->data[2] << 8) |
                                                     packet->data[3];
                            k230_receiving_image = 1;
                            k230_image_received_size = 0;

                            // ???????????????????????
                            if (packet->length > 4) {
                                u16 img_data_len = packet->length - 4;
                                if (img_data_len <= TCM_IMAGE_BUFFER_SIZE) {
                                    memcpy(k230_image_buffer, &packet->data[4], img_data_len);
                                    k230_image_received_size = img_data_len;
                                }
                            }
                        }
                    } else {
                        // ?????????????????
                        if (k230_image_received_size + packet->length <= TCM_IMAGE_BUFFER_SIZE) {
                            memcpy(&k230_image_buffer[k230_image_received_size],
                                   packet->data, packet->length);
                            k230_image_received_size += packet->length;
                        }
                    }

                    // ?????????????
                    if (k230_image_received_size >= k230_expected_image_size) {
                        current_image.width = K230_IMG_WIDTH;
                        current_image.height = K230_IMG_HEIGHT;
                        current_image.format = K230_IMG_FORMAT_JPEG;
                        current_image.size = k230_image_received_size;
                        current_image.data = k230_image_buffer;
                        k230_state = K230_STATE_IDLE;
                        k230_receiving_image = 0;
                    }
                }
            } else {
                k230_state = K230_STATE_ERROR;
            }
        } else {
            k230_state = K230_STATE_ERROR;
        }

        k230_rx_complete = 0;
        k230_rx_index = 0;
    }
}

u8 k230_calculate_checksum(u8* data, u16 len) {
    u8 checksum = 0;
    for (u16 i = 0; i < len; i++) {
        checksum ^= data[i];
    }
    return checksum;
}

// UART7???????????????main.c??UART7_IRQHandler??????
void k230_uart_rx_handler(u8 data) {
    if (k230_rx_index < MAX_PACKET_SIZE) {
        k230_rx_buffer[k230_rx_index++] = data;

        // ?????????????????
        if (k230_rx_index >= 6) { // ???????????header+cmd+length+checksum+footer
            k230_packet_t* packet = (k230_packet_t*)k230_rx_buffer;

            // ??????
            if (packet->header == PROTOCOL_HEADER) {
                u16 expected_len = sizeof(packet->header) + sizeof(packet->cmd) +
                                  sizeof(packet->length) + packet->length +
                                  sizeof(packet->checksum) + sizeof(packet->footer);

                // ???????????????
                if (k230_rx_index >= expected_len) {
                    // ???????
                    if (k230_rx_buffer[expected_len - 1] == PROTOCOL_FOOTER) {
                        k230_rx_complete = 1;
                    } else {
                        // ???????????????
                        k230_rx_index = 0;
                    }
                }
            } else {
                // ??????????????????????
                u16 i;
                for (i = 1; i < k230_rx_index; i++) {
                    if (k230_rx_buffer[i] == PROTOCOL_HEADER) {
                        memmove(k230_rx_buffer, &k230_rx_buffer[i], k230_rx_index - i);
                        k230_rx_index -= i;
                        break;
                    }
                }
                if (i == k230_rx_index) {
                    k230_rx_index = 0; // ?????????????
                }
            }
        }
    } else {
        // ??????????????
        k230_rx_index = 0;
    }
}
