#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K230 Simplified Camera Program (UTF-8)
Use CanMV K230 API to implement UART controlled photography
"""

import time
import os
from machine import UART
import sensor
import image
import lcd

class SimpleK230Camera:
    def __init__(self):
        # UART config - 115200 baud rate, corresponds to CH32V307 UART6
        self.uart = UART(1, baudrate=115200, bits=8, parity=None, stop=1, timeout=1000, read_buf_len=4096)
        
        # Photo counter
        self.photo_count = 0
        
        # Initialize camera and display
        self.init_camera()
        
    def init_camera(self):
        """Initialize camera"""
        try:
            # Initialize LCD display
            lcd.init()
            lcd.clear()
            
            # Initialize camera sensor
            sensor.reset()
            sensor.set_pixformat(sensor.RGB565)  # Set pixel format
            sensor.set_framesize(sensor.QVGA)   # Set frame size 320x240
            sensor.skip_frames(time=2000)       # Skip first 2 seconds of frames
            
            print("K230 camera initialized successfully")
            self.send_uart("CAMERA_INIT_OK")
            
        except Exception as e:
            print(f"Camera initialization failed: {e}")
            self.send_uart("CAMERA_INIT_FAIL")
    
    def send_uart(self, msg):
        """Send message to UART"""
        try:
            response = f"{msg}\r\n"
            self.uart.write(response)
            print(f"UART send: {msg}")
        except Exception as e:
            print(f"UART send failed: {e}")
    
    def capture_and_save(self):
        """Take photo and save to current directory"""
        try:
            # Capture image
            img = sensor.snapshot()
            if img is None:
                print("Image capture failed")
                self.send_uart("PHOTO_FAIL")
                return False
            
            # Generate filename
            timestamp = time.ticks_ms()
            filename = f"photo_{self.photo_count:04d}_{timestamp}.jpg"
            
            # Save image
            try:
                img.save(filename)
                self.photo_count += 1
                print(f"Photo taken successfully: {filename}")
                self.send_uart(f"PHOTO_OK:{filename}")
                
                # Display on LCD
                lcd.display(img)
                return True
                
            except Exception as e:
                print(f"Save failed: {e}")
                self.send_uart("PHOTO_SAVE_FAIL")
                return False
                
        except Exception as e:
            print(f"Photo error: {e}")
            self.send_uart("PHOTO_ERROR")
            return False
    
    def process_uart_command(self, cmd):
        """Process UART command"""
        cmd = cmd.strip().lower()
        print(f"Received command: '{cmd}'")
        
        if cmd == "photo":
            print("Execute photo")
            self.capture_and_save()
        elif cmd == "status":
            status_msg = f"STATUS_OK:COUNT_{self.photo_count}"
            self.send_uart(status_msg)
        elif cmd == "test":
            self.send_uart("TEST_OK")
        elif cmd == "reset":
            self.photo_count = 0
            self.send_uart("RESET_OK")
        else:
            print(f"Unknown command: {cmd}")
            self.send_uart(f"UNKNOWN:{cmd}")
    
    def run_main_loop(self):
        """Main loop"""
        print("K230 camera program started")
        self.send_uart("K230_READY")
        
        uart_buffer = ""
        
        while True:
            try:
                # Process UART reception
                if self.uart.any():
                    data = self.uart.read()
                    if data:
                        try:
                            uart_buffer += data.decode('utf-8')
                        except:
                            uart_buffer += data.decode('utf-8', errors='ignore')
                        
                        # Process complete commands (split by newline)
                        while '\n' in uart_buffer:
                            line, uart_buffer = uart_buffer.split('\n', 1)
                            line = line.strip('\r\n ')
                            if line:
                                self.process_uart_command(line)
                
                # Real-time preview
                try:
                    img = sensor.snapshot()
                    if img:
                        lcd.display(img)
                except:
                    pass  # Preview failure does not affect main function
                
                time.sleep_ms(50)  # 50ms delay
                
            except KeyboardInterrupt:
                print("Program interrupted and exited")
                break
            except Exception as e:
                print(f"Main loop error: {e}")
                time.sleep_ms(100)

def main():
    """Main function"""
    print("=== K230 Camera Control Program ===")
    print("Supported commands:")
    print("  photo  - Take photo and save")
    print("  status - Query status")
    print("  test   - Test connection")
    print("  reset  - Reset counter")
    print("====================================")
    
    try:
        camera = SimpleK230Camera()
        camera.run_main_loop()
    except Exception as e:
        print(f"Program exception: {e}")
    finally:
        print("Program ended")

if __name__ == "__main__":
    main()
