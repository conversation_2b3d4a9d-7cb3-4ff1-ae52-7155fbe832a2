#!/usr/bin/env python3
# -*- coding: gb2312 -*-
"""
K230������ - GB2312�����汾
����K230 CanMV��������any()��������
֧����CH32V307 UART6ͨ��
"""

import time
import os
from machine import UART

class K230Main:
    def __init__(self):
        """��ʼ��K230����"""
        print("=== K230���������� ===")

        # UART��ʼ�� - ��ӦCH32V307��UART6(PC0/PC1)
        try:
            self.uart = UART(1, 115200)
            print("UART��ʼ���ɹ� (115200������)")
        except Exception as e:
            print(f"UART��ʼ��ʧ��: {e}")
            self.uart = None

        # ������
        self.photo_count = 0
        self.test_count = 0

        print("K230������ʼ������")
        self.send_message("INIT_OK")

    def send_message(self, msg):
        """������Ϣ��UART"""
        if not self.uart:
            print(f"[��UART] Ӧ����: {msg}")
            return

        try:
            data = f"{msg}\r\n".encode('utf-8')
            self.uart.write(data)
            print(f"����: {msg}")
        except Exception as e:
            print(f"����ʧ��: {e}")

    def read_uart_data(self):
        """��ȡUART���� - ��ʹ��any()����"""
        if not self.uart:
            return None

        try:
            # ֱ�Ӷ�ȡ���ݣ�������any()
            data = self.uart.read(128)  # ��ȡ����128�ֽ�
            return data
        except Exception as e:
            return None

    def create_test_photo(self):
        """����������Ƭ�ļ�"""
        try:
            timestamp = time.ticks_ms()
            filename = f"photo_{self.photo_count:04d}_{timestamp}.txt"

            # ���������ļ�
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"K230������Ƭ #{self.photo_count}\n")
                f.write(f"ʱ����: {timestamp}\n")
                f.write(f"�ļ���: {filename}\n")
                f.write(f"UARTͨ�Ų��Գɹ�\n")
                f.write(f"����: UTF-8\n")

            self.photo_count += 1
            print(f"������Ƭ�Ѵ���: {filename}")
            self.send_message(f"PHOTO_OK:{filename}")
            return True

        except Exception as e:
            print(f"��Ƭ����ʧ��: {e}")
            self.send_message("PHOTO_FAIL")
            return False

    def process_command(self, cmd):
        """�������յ�������"""
        cmd = cmd.strip().lower()
        print(f"��������: '{cmd}'")

        if cmd == "photo":
            self.create_test_photo()

        elif cmd == "test":
            self.test_count += 1
            self.send_message(f"TEST_OK:{self.test_count}")

        elif cmd == "ping":
            self.send_message("PONG")

        elif cmd == "count":
            self.send_message(f"COUNT:{self.photo_count}")

        elif cmd == "status":
            self.send_message(f"STATUS_OK:PHOTOS_{self.photo_count}_TESTS_{self.test_count}")

        elif cmd == "reset":
            self.photo_count = 0
            self.test_count = 0
            self.send_message("RESET_OK")

        elif cmd == "hello":
            self.send_message("HELLO_K230_GB2312")

        elif cmd == "info":
            self.send_message(f"INFO:K230_CANMV_GB2312_PHOTOS_{self.photo_count}")

        else:
            self.send_message(f"UNKNOWN:{cmd}")

    def main_loop(self):
        """��ѭ��"""
        print("K230��������ʼ����")
        self.send_message("K230_READY")

        buffer = ""
        loop_count = 0
        last_heartbeat = 0

        while True:
            try:
                # ��ȡUART����
                data = self.read_uart_data()

                if data:
                    try:
                        # �������յ�������
                        if isinstance(data, bytes):
                            text = data.decode('utf-8', errors='ignore')
                        else:
                            text = str(data)

                        buffer += text

                        # ����������������
                        while '\n' in buffer:
                            line, buffer = buffer.split('\n', 1)
                            command = line.strip('\r\n ')

                            if command:
                                print(f"���յ�: {command}")
                                self.process_command(command)

                    except Exception as e:
                        print(f"���ݴ�������: {e}")

                # ������״̬��ʾ
                loop_count += 1
                current_time = time.ticks_ms()

                # ÿ10�뷢��һ������
                if current_time - last_heartbeat > 10000:
                    print(f"���� - ��Ƭ: {self.photo_count}, ����: {self.test_count}, ѭ��: {loop_count}")
                    self.send_message("HEARTBEAT")
                    last_heartbeat = current_time

                time.sleep_ms(50)  # 50ms��ʱ

            except KeyboardInterrupt:
                print("\n�������û��ж�")
                break
            except Exception as e:
                print(f"��ѭ������: {e}")
                time.sleep_ms(100)

        print("K230��������")

def main():
    """������"""
    print("=" * 50)
    print("    K230 CanMV ������ (GB2312����)")
    print("=" * 50)
    print("֧������:")
    print("  photo  - ����������Ƭ�ļ�")
    print("  test   - ��������")
    print("  ping   - Ping����")
    print("  count  - ��ѯ��Ƭ����")
    print("  status - ��ѯ״̬")
    print("  reset  - ���ü�����")
    print("  hello  - �ʺ�����")
    print("  info   - ϵͳ��Ϣ")
    print("=" * 50)

    try:
        app = K230Main()
        app.main_loop()
    except Exception as e:
        print(f"�����쳣: {e}")
        try:
            import sys
            sys.print_exception(e)
        except:
            pass

if __name__ == "__main__":
    main()
