#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K230 Test Program - Minimal Version for Testing
Works without sensor module dependencies
"""

import time
import os
from machine import UART

class K230Test:
    def __init__(self):
        # UART config - corresponds to CH32V307 UART6(PC0/PC1)
        try:
            # Try simple UART initialization first
            self.uart = UART(1, 115200)
            print("UART initialized with basic parameters")
        except Exception as e:
            print(f"Basic UART init failed: {e}")
            try:
                # Try with minimal parameters
                self.uart = UART(1)
                print("UART initialized with minimal parameters")
            except Exception as e2:
                print(f"Minimal UART init failed: {e2}")
                self.uart = None

        # Photo counter
        self.photo_count = 0

        print("K230 Test initialized")
        if self.uart:
            self.send_uart("INIT_OK")
        else:
            print("Warning: UART not available")
    
    def send_uart(self, msg):
        """Send message to UART"""
        if not self.uart:
            print(f"UART not available, would send: {msg}")
            return

        try:
            # Convert to bytes for better compatibility
            data = f"{msg}\r\n".encode('utf-8')
            self.uart.write(data)
            print(f"Send: {msg}")
        except Exception as e:
            print(f"UART send failed: {e}")
    
    def simulate_photo(self):
        """Simulate taking photo"""
        try:
            # Generate filename
            filename = f"test_pic_{self.photo_count:03d}.txt"
            
            # Create test file
            with open(filename, 'w') as f:
                f.write(f"Test photo {self.photo_count}\n")
                f.write(f"Timestamp: {time.ticks_ms()}\n")
                f.write(f"UART test successful\n")
            
            self.photo_count += 1
            print(f"Test photo created: {filename}")
            self.send_uart(f"PHOTO_OK:{filename}")
            return True
            
        except Exception as e:
            print(f"Test photo failed: {e}")
            self.send_uart("PHOTO_FAIL")
            return False
    
    def process_cmd(self, cmd):
        """Process command"""
        cmd = cmd.strip().lower()
        print(f"Processing command: '{cmd}'")
        
        if cmd == "photo":
            self.simulate_photo()
        elif cmd == "test":
            self.send_uart("TEST_OK")
        elif cmd == "count":
            self.send_uart(f"COUNT:{self.photo_count}")
        elif cmd == "status":
            self.send_uart(f"STATUS_OK:COUNT_{self.photo_count}")
        elif cmd == "reset":
            self.photo_count = 0
            self.send_uart("RESET_OK")
        elif cmd == "ping":
            self.send_uart("PONG")
        else:
            self.send_uart(f"UNKNOWN:{cmd}")
    
    def main_loop(self):
        """Main program loop"""
        print("K230 Test Program started")
        self.send_uart("K230_READY")
        
        buffer = ""
        loop_count = 0
        
        while True:
            try:
                # Check UART data
                if self.uart and self.uart.any():
                    data = self.uart.read()
                    if data:
                        try:
                            if isinstance(data, bytes):
                                buffer += data.decode('utf-8')
                            else:
                                buffer += str(data)
                        except:
                            try:
                                buffer += data.decode('utf-8', errors='ignore')
                            except:
                                buffer += str(data)

                        # Process complete commands
                        while '\n' in buffer:
                            line, buffer = buffer.split('\n', 1)
                            cmd = line.strip('\r\n ')
                            if cmd:
                                print(f"Received: {cmd}")
                                self.process_cmd(cmd)
                
                # Heartbeat every 5 seconds
                loop_count += 1
                if loop_count % 100 == 0:  # 100 * 50ms = 5 seconds
                    print(f"Heartbeat - Photos taken: {self.photo_count}")
                
                time.sleep_ms(50)
                
            except KeyboardInterrupt:
                print("Program interrupted")
                break
            except Exception as e:
                print(f"Error: {e}")
                time.sleep_ms(100)
        
        print("Program ended")

def main():
    """Main function"""
    print("=== K230 Test Program ===")
    print("This is a test version without camera dependencies")
    print("Supported commands:")
    print("  photo  - Create test file")
    print("  test   - Test connection")
    print("  count  - Query photo count")
    print("  status - Query status")
    print("  reset  - Reset counter")
    print("  ping   - Ping test")
    print("========================")
    
    try:
        test_app = K230Test()
        test_app.main_loop()
    except Exception as e:
        print(f"Program exception: {e}")

if __name__ == "__main__":
    main()
