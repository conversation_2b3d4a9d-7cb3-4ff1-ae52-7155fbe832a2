#include "display_driver.h"
#include "uart_utf8.h"
#include "string.h"
#include "stdio.h"

// ??????
display_page_t current_page = PAGE_MAIN;

void display_init(void) {
    // UART8????main.c???????
    current_page = PAGE_MAIN;
    display_clear_screen();
    display_main_page();
}

int display_set_page(display_page_t page) {
    u8 page_data = (u8)page;
    current_page = page;
    return display_send_command(DISPLAY_CMD_SET_PAGE, &page_data, 1);
}

int display_show_text(u16 x, u16 y, const char* text, u16 color) {
    if (!text) return ERR_INVALID_PARAM;
    
    u8 cmd_data[256];
    u16 text_len = strlen(text);
    if (text_len > 240) text_len = 240; // ???????????
    
    cmd_data[0] = x >> 8;
    cmd_data[1] = x & 0xFF;
    cmd_data[2] = y >> 8;
    cmd_data[3] = y & 0xFF;
    cmd_data[4] = color >> 8;
    cmd_data[5] = color & 0xFF;
    cmd_data[6] = text_len;
    memcpy(&cmd_data[7], text, text_len);
    
    return display_send_command(DISPLAY_CMD_SHOW_TEXT, cmd_data, 7 + text_len);
}

int display_clear_screen(void) {
    return display_send_command(DISPLAY_CMD_CLEAR, NULL, 0);
}

int display_show_tcm_result(const tcm_result_t* result) {
    if (!result) return ERR_INVALID_PARAM;
    
    display_clear_screen();
    display_set_page(PAGE_RESULT);
    
    char buffer[128];
    
    // ???????
    display_show_text(10, 10, "?????????", 0xFFFF);
    
    // ??????
    snprintf(buffer, sizeof(buffer), "???: %s", result->tongue_color);
    display_show_text(10, 40, buffer, 0x07E0);
    
    // ??????
    snprintf(buffer, sizeof(buffer), "???: %s", result->tongue_coating);
    display_show_text(10, 70, buffer, 0x07E0);
    
    // ???????
    snprintf(buffer, sizeof(buffer), "????: %s", result->moisture);
    display_show_text(10, 100, buffer, 0x07E0);
    
    // ?????
    snprintf(buffer, sizeof(buffer), "??: %s", result->thickness);
    display_show_text(10, 130, buffer, 0x07E0);
    
    // ????????
    display_show_text(10, 160, "???:", 0xF800);
    display_show_text(10, 190, result->diagnosis, 0xF800);
    
    // ????????
    snprintf(buffer, sizeof(buffer), "?????: %d%%", result->confidence);
    display_show_text(10, 220, buffer, 0x001F);
    
    return ERR_OK;
}

int display_show_progress(u8 percent) {
    char buffer[32];
    snprintf(buffer, sizeof(buffer), "????????: %d%%", percent);
    return display_show_text(10, 120, buffer, 0x07E0);
}

int display_send_command(u8 cmd, u8* data, u16 len) {
    display_packet_t packet;
    packet.header = PROTOCOL_HEADER;
    packet.cmd = cmd;
    packet.length = len;
    
    if (data && len > 0 && len <= 256) {
        memcpy(packet.data, data, len);
    }
    
    // ?????????
    u8 checksum = packet.header ^ packet.cmd ^ (packet.length >> 8) ^ (packet.length & 0xFF);
    for (u16 i = 0; i < len; i++) {
        checksum ^= packet.data[i];
    }
    packet.checksum = checksum;
    packet.footer = PROTOCOL_FOOTER;
    
    // ?????????
    u16 total_len = sizeof(packet.header) + sizeof(packet.cmd) + sizeof(packet.length) + 
                   len + sizeof(packet.checksum) + sizeof(packet.footer);
    uart8_send_string((u8*)&packet, total_len);
    
    return ERR_OK;
}

void display_main_page(void) {
    display_clear_screen();
    display_set_page(PAGE_MAIN);
    display_show_text(50, 50, "?????????", 0xFFFF);
    display_show_text(50, 100, "?????????", 0x07E0);
    display_show_text(50, 130, "?????", 0x07E0);
    display_show_text(50, 180, "V1.0", 0x001F);
}

// ??????????
int display_handle_touch_event(u16 x, u16 y) {
    switch(current_page) {
        case PAGE_MAIN:
            // ???????????????????????????????
            return 1; // ????1???????????

        case PAGE_RESULT:
            // ?????????????????????
            display_main_page();
            return 0;

        default:
            return 0;
    }
}

void display_capture_page(void) {
    display_clear_screen();
    display_set_page(PAGE_CAPTURE);
    display_show_text(50, 50, "????????", 0xFFFF);
    display_show_text(50, 100, "??????", 0x07E0);
    display_show_text(50, 150, "????????...", 0xF800);
}

void display_analysis_page(u8 progress) {
    display_clear_screen();
    display_set_page(PAGE_ANALYSIS);
    display_show_text(50, 50, "AI??????", 0xFFFF);
    display_show_text(50, 100, "?????...", 0x07E0);
    display_show_progress(progress);
}
