#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K230 UART Test Tool (UTF-8)
Test K230 communication via serial port
"""

import serial
import time
import sys

class K230Tester:
    def __init__(self, port='COM3', baudrate=115200):
        """Initialize serial connection"""
        try:
            self.ser = serial.Serial(port, baudrate, timeout=1)
            print(f"Connected to {port} successfully, baudrate: {baudrate}")
        except Exception as e:
            print(f"Serial connection failed: {e}")
            print("Available ports:")
            import serial.tools.list_ports
            ports = serial.tools.list_ports.comports()
            for port in ports:
                print(f"  {port.device} - {port.description}")
            sys.exit(1)
    
    def send_command(self, cmd):
        """Send command to K230"""
        try:
            command = f"{cmd}\r\n"
            self.ser.write(command.encode())
            print(f"Sent: {cmd}")
            
            # Wait for response
            time.sleep(0.5)
            if self.ser.in_waiting > 0:
                response = self.ser.read(self.ser.in_waiting).decode('utf-8', errors='ignore')
                print(f"Received: {response.strip()}")
                return response
            else:
                print("No response")
                return None
                
        except Exception as e:
            print(f"Send command failed: {e}")
            return None
    
    def test_photo(self):
        """Test photo command"""
        print("\n=== Photo Test ===")
        response = self.send_command("photo")
        
        if response and "PHOTO_OK" in response:
            print("? Photo successful!")
        elif response and "PHOTO_FAIL" in response:
            print("? Photo failed!")
        else:
            print("? Unknown photo response")
    
    def test_connection(self):
        """Test basic connection"""
        print("\n=== Connection Test ===")
        
        # Test ping
        response = self.send_command("ping")
        if response and "PONG" in response:
            print("? Ping test passed")
        else:
            print("? Ping test failed")
        
        # Test basic command
        response = self.send_command("test")
        if response and "TEST_OK" in response:
            print("? Test command passed")
        else:
            print("? Test command failed")
    
    def test_status(self):
        """Test status commands"""
        print("\n=== Status Test ===")
        
        # Query count
        response = self.send_command("count")
        if response:
            print(f"Count response: {response.strip()}")
        
        # Query status
        response = self.send_command("status")
        if response:
            print(f"Status response: {response.strip()}")
    
    def interactive_mode(self):
        """Interactive command mode"""
        print("\n=== Interactive Mode ===")
        print("Enter commands (type 'quit' to exit):")
        print("Available commands: photo, test, ping, count, status, reset")
        
        while True:
            try:
                cmd = input("K230> ").strip()
                if cmd.lower() in ['quit', 'exit', 'q']:
                    break
                elif cmd:
                    self.send_command(cmd)
                    
            except KeyboardInterrupt:
                break
        
        print("Interactive mode ended")
    
    def auto_test_sequence(self):
        """Automatic test sequence"""
        print("\n=== Automatic Test Sequence ===")
        
        commands = [
            ("ping", "PONG"),
            ("test", "TEST_OK"), 
            ("count", "COUNT:"),
            ("photo", "PHOTO_OK:"),
            ("count", "COUNT:"),
            ("status", "STATUS_OK:"),
            ("reset", "RESET_OK"),
            ("count", "COUNT:0")
        ]
        
        passed = 0
        total = len(commands)
        
        for cmd, expected in commands:
            print(f"\nTesting: {cmd}")
            response = self.send_command(cmd)
            
            if response and expected in response:
                print(f"? PASS - Expected '{expected}' found")
                passed += 1
            else:
                print(f"? FAIL - Expected '{expected}' not found")
            
            time.sleep(1)  # Wait between commands
        
        print(f"\n=== Test Results ===")
        print(f"Passed: {passed}/{total}")
        print(f"Success rate: {passed/total*100:.1f}%")
    
    def monitor_mode(self):
        """Monitor K230 output"""
        print("\n=== Monitor Mode ===")
        print("Monitoring K230 output (Ctrl+C to stop)...")
        
        try:
            while True:
                if self.ser.in_waiting > 0:
                    data = self.ser.read(self.ser.in_waiting).decode('utf-8', errors='ignore')
                    print(f"K230: {data.strip()}")
                time.sleep(0.1)
                
        except KeyboardInterrupt:
            print("\nMonitor mode ended")
    
    def close(self):
        """Close serial connection"""
        if self.ser:
            self.ser.close()
            print("Serial connection closed")

def main():
    """Main function"""
    print("=== K230 UART Test Tool ===")
    
    # Check command line arguments
    port = 'COM3'  # Default port
    if len(sys.argv) > 1:
        port = sys.argv[1]
    
    try:
        tester = K230Tester(port)
        
        while True:
            print("\n=== Test Menu ===")
            print("1. Connection Test")
            print("2. Photo Test") 
            print("3. Status Test")
            print("4. Auto Test Sequence")
            print("5. Interactive Mode")
            print("6. Monitor Mode")
            print("0. Exit")
            
            choice = input("Select option (0-6): ").strip()
            
            if choice == '1':
                tester.test_connection()
            elif choice == '2':
                tester.test_photo()
            elif choice == '3':
                tester.test_status()
            elif choice == '4':
                tester.auto_test_sequence()
            elif choice == '5':
                tester.interactive_mode()
            elif choice == '6':
                tester.monitor_mode()
            elif choice == '0':
                break
            else:
                print("Invalid option")
        
    except Exception as e:
        print(f"Test tool error: {e}")
    
    finally:
        try:
            tester.close()
        except:
            pass

if __name__ == "__main__":
    main()
