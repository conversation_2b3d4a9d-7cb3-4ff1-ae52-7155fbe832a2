#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K230 UART Test - Simple UART communication test
For K230 CanMV environment without any() method
"""

import time
from machine import UART

def uart_send_test():
    """Test UART sending only"""
    print("=== UART Send Test ===")
    
    try:
        uart = UART(1, 115200)
        print("UART created")
        
        # Send test messages
        messages = [
            "INIT_OK",
            "K230_READY", 
            "TEST_OK",
            "PHOTO_OK:test.jpg",
            "STATUS_OK:COUNT_0"
        ]
        
        for i, msg in enumerate(messages):
            try:
                data = f"{msg}\r\n".encode('utf-8')
                uart.write(data)
                print(f"Sent {i+1}: {msg}")
                time.sleep_ms(1000)
            except Exception as e:
                print(f"Send error: {e}")
        
        print("? Send test completed")
        
    except Exception as e:
        print(f"UART send test failed: {e}")

def uart_echo_test():
    """Test UART echo - send and try to receive"""
    print("=== UART Echo Test ===")
    
    try:
        uart = UART(1, 115200)
        print("UART created for echo test")
        
        # Send initial message
        uart.write(b"K230_READY\r\n")
        print("Sent: K230_READY")
        
        # Simple loop to check for responses
        for i in range(20):  # 20 seconds
            try:
                # Try to read data
                data = uart.read(32)  # Read up to 32 bytes
                
                if data:
                    try:
                        text = data.decode('utf-8', errors='ignore')
                        print(f"Received: {repr(text)}")
                        
                        # Echo back
                        response = f"ECHO_{i}:{text.strip()}\r\n"
                        uart.write(response.encode('utf-8'))
                        print(f"Echoed: {response.strip()}")
                        
                    except Exception as e:
                        print(f"Data processing error: {e}")
                
                else:
                    # No data, send heartbeat
                    if i % 5 == 0:
                        heartbeat = f"HEARTBEAT_{i}\r\n"
                        uart.write(heartbeat.encode('utf-8'))
                        print(f"Heartbeat: {heartbeat.strip()}")
                
                time.sleep_ms(1000)
                
            except Exception as e:
                print(f"Loop {i} error: {e}")
        
        print("? Echo test completed")
        
    except Exception as e:
        print(f"UART echo test failed: {e}")

def simple_command_processor():
    """Simple command processor without any()"""
    print("=== Simple Command Processor ===")
    
    try:
        uart = UART(1, 115200)
        print("UART ready for commands")
        
        # Send ready signal
        uart.write(b"K230_READY\r\n")
        print("Sent: K230_READY")
        
        buffer = ""
        count = 0
        
        for loop in range(100):  # Run for 100 loops (50 seconds)
            try:
                # Try to read data
                data = uart.read(64)
                
                if data:
                    # Process received data
                    try:
                        text = data.decode('utf-8', errors='ignore')
                        buffer += text
                        
                        # Process complete lines
                        while '\n' in buffer:
                            line, buffer = buffer.split('\n', 1)
                            cmd = line.strip('\r\n ')
                            
                            if cmd:
                                print(f"Command: {cmd}")
                                
                                # Process commands
                                if cmd.lower() == "photo":
                                    count += 1
                                    response = f"PHOTO_OK:pic_{count:03d}.jpg\r\n"
                                    uart.write(response.encode('utf-8'))
                                    print(f"Response: {response.strip()}")
                                    
                                elif cmd.lower() == "test":
                                    uart.write(b"TEST_OK\r\n")
                                    print("Response: TEST_OK")
                                    
                                elif cmd.lower() == "count":
                                    response = f"COUNT:{count}\r\n"
                                    uart.write(response.encode('utf-8'))
                                    print(f"Response: {response.strip()}")
                                    
                                else:
                                    response = f"UNKNOWN:{cmd}\r\n"
                                    uart.write(response.encode('utf-8'))
                                    print(f"Response: {response.strip()}")
                    
                    except Exception as e:
                        print(f"Data processing error: {e}")
                
                # Heartbeat every 10 loops
                if loop % 10 == 0:
                    heartbeat = f"ALIVE_{loop}\r\n"
                    uart.write(heartbeat.encode('utf-8'))
                    print(f"Heartbeat: ALIVE_{loop}")
                
                time.sleep_ms(500)
                
            except Exception as e:
                print(f"Loop error: {e}")
                time.sleep_ms(100)
        
        print("? Command processor completed")
        
    except Exception as e:
        print(f"Command processor failed: {e}")

def main():
    """Main function - choose test to run"""
    print("=== K230 UART Test Suite ===")
    print("Testing UART without any() method")
    print("=============================")
    
    # Run tests in sequence
    try:
        print("\n1. Testing UART send...")
        uart_send_test()
        
        print("\n2. Testing UART echo...")
        uart_echo_test()
        
        print("\n3. Testing command processor...")
        simple_command_processor()
        
    except Exception as e:
        print(f"Test suite error: {e}")
    
    print("\n=== All tests completed ===")

if __name__ == "__main__":
    main()
