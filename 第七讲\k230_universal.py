#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K230 Universal Camera Program - Compatible Version
Works with different K230 environments and APIs
"""

import time
import os
from machine import UART

# Try to import different camera modules based on available APIs
camera_api = None
lcd_api = None

try:
    # Try CanMV K230 API first
    import sensor
    import image
    import lcd
    camera_api = "canmv"
    lcd_api = lcd
    print("Using CanMV K230 API")
except ImportError:
    try:
        # Try alternative K230 API
        from media.camera import *
        from media.display import *
        from media.media import *
        camera_api = "media"
        print("Using Media API")
    except ImportError:
        try:
            # Try basic camera module
            import camera
            camera_api = "basic"
            print("Using Basic Camera API")
        except ImportError:
            print("Warning: No camera module found, running in simulation mode")
            camera_api = "simulation"

class K230UniversalCamera:
    def __init__(self):
        # UART config - corresponds to CH32V307 UART6(PC0/PC1)
        self.uart = UART(1, 115200, 8, None, 1, timeout=1000, read_buf_len=1024)
        
        # Photo counter
        self.photo_count = 0
        
        # Initialize camera based on available API
        self.init_camera()
    
    def init_camera(self):
        """Initialize camera based on available API"""
        global camera_api, lcd_api
        
        try:
            if camera_api == "canmv":
                # CanMV K230 API
                if lcd_api:
                    lcd_api.init()
                    lcd_api.clear()
                
                sensor.reset()
                sensor.set_pixformat(sensor.RGB565)
                sensor.set_framesize(sensor.QVGA)  # 320x240
                sensor.skip_frames(time=2000)
                
            elif camera_api == "media":
                # Media API initialization
                media.initialize()
                camera.sensor_init(CAM_DEV_ID_0, CAM_DEFAULT_SENSOR)
                camera.set_outsize(CAM_DEV_ID_0, CAM_CHN_ID_0, 320, 240)
                camera.set_outfmt(CAM_DEV_ID_0, CAM_CHN_ID_0, PIXEL_FORMAT_RGB_565)
                camera.start_stream(CAM_DEV_ID_0)
                
            elif camera_api == "basic":
                # Basic camera API
                camera.init()
                
            else:
                # Simulation mode
                print("Running in simulation mode - no actual camera")
            
            print("Camera initialization completed")
            self.send_uart("INIT_OK")
            
        except Exception as e:
            print(f"Camera initialization failed: {e}")
            self.send_uart("INIT_FAIL")
    
    def send_uart(self, msg):
        """Send message to UART"""
        try:
            self.uart.write(f"{msg}\r\n")
            print(f"Send: {msg}")
        except Exception as e:
            print(f"UART send failed: {e}")
    
    def take_photo(self):
        """Take photo and save"""
        global camera_api, lcd_api
        
        try:
            img = None
            
            if camera_api == "canmv":
                # CanMV API
                img = sensor.snapshot()
                
            elif camera_api == "media":
                # Media API
                img = camera.capture_image(CAM_DEV_ID_0, CAM_CHN_ID_0)
                
            elif camera_api == "basic":
                # Basic API
                img = camera.capture()
                
            else:
                # Simulation mode - create dummy file
                filename = f"sim_pic_{self.photo_count:03d}.txt"
                with open(filename, 'w') as f:
                    f.write(f"Simulation photo {self.photo_count} taken at {time.ticks_ms()}")
                self.photo_count += 1
                print(f"Simulation photo saved: {filename}")
                self.send_uart(f"PHOTO_OK:{filename}")
                return True
            
            if img is None:
                print("Image capture failed")
                self.send_uart("PHOTO_FAIL")
                return False
            
            # Generate filename
            filename = f"pic_{self.photo_count:03d}.jpg"
            
            # Save image
            if hasattr(img, 'save'):
                img.save(filename)
            else:
                # Alternative save method
                with open(filename, 'wb') as f:
                    f.write(bytes(img))
            
            self.photo_count += 1
            print(f"Photo taken successfully: {filename}")
            self.send_uart(f"PHOTO_OK:{filename}")
            
            # Display on LCD if available
            if camera_api == "canmv" and lcd_api:
                try:
                    lcd_api.display(img)
                except:
                    pass
            
            return True
            
        except Exception as e:
            print(f"Photo failed: {e}")
            self.send_uart("PHOTO_FAIL")
            return False
    
    def process_cmd(self, cmd):
        """Process command"""
        cmd = cmd.strip().lower()
        
        if cmd == "photo":
            self.take_photo()
        elif cmd == "test":
            self.send_uart("TEST_OK")
        elif cmd == "count":
            self.send_uart(f"COUNT:{self.photo_count}")
        elif cmd == "api":
            self.send_uart(f"API:{camera_api}")
        elif cmd == "reset":
            self.photo_count = 0
            self.send_uart("RESET_OK")
        else:
            self.send_uart(f"UNKNOWN:{cmd}")
    
    def run_preview(self):
        """Run preview if possible"""
        global camera_api, lcd_api
        
        try:
            if camera_api == "canmv":
                img = sensor.snapshot()
                if img and lcd_api:
                    lcd_api.display(img)
            elif camera_api == "media":
                img = camera.capture_image(CAM_DEV_ID_0, CAM_CHN_ID_0)
                # Display logic for media API
            # Skip preview for other APIs to avoid errors
        except:
            pass  # Preview failure doesn't affect main function
    
    def main_loop(self):
        """Main program loop"""
        print(f"K230 Universal Camera started (API: {camera_api})")
        self.send_uart("K230_READY")
        
        buffer = ""
        while True:
            try:
                # Check UART data
                if self.uart.any():
                    data = self.uart.read()
                    if data:
                        buffer += data.decode('utf-8', errors='ignore')
                        
                        # Process complete commands
                        while '\n' in buffer:
                            line, buffer = buffer.split('\n', 1)
                            cmd = line.strip('\r\n ')
                            if cmd:
                                print(f"Received: {cmd}")
                                self.process_cmd(cmd)
                
                # Run preview
                self.run_preview()
                
                time.sleep_ms(50)
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"Error: {e}")
                time.sleep_ms(100)
        
        print("Program ended")

def main():
    """Main function"""
    print("=== K230 Universal Camera Program ===")
    print("Supported commands:")
    print("  photo  - Take photo and save")
    print("  test   - Test connection")
    print("  count  - Query photo count")
    print("  api    - Query current API")
    print("  reset  - Reset counter")
    print("=====================================")
    
    try:
        camera = K230UniversalCamera()
        camera.main_loop()
    except Exception as e:
        print(f"Program exception: {e}")

if __name__ == "__main__":
    main()
