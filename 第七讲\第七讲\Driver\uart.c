#include "debug.h"
#include "ringbuffer.h"
#include <stdio.h>
#include "uart.h"
#include <stdarg.h> // ?????????
#include <string.h> // ?????????


UART_DMA_Buff uart_dma_rx_buff = {0};

// ?????printf?????????????UART?????????????
int my_printf(USART_TypeDef* USARTx, const char *format, ...)
{
    char buffer[512]; // ???????????????????
    va_list arg;      // ??????????
    int len;          // ?????????????

    // ???????????????
    va_start(arg, format);
    // ???????????????? buffer
    len = vsnprintf(buffer, sizeof(buffer), format, arg);
    // ???????????????????
    va_end(arg);

    // ??? CH32 ??????? buffer ????????
    for (int i = 0; i < len; i++) {
        while (USART_GetFlagStatus(USARTx, USART_FLAG_TXE) == RESET);
        USART_SendData(USARTx, (uint8_t)buffer[i]);
    }

    return len;
}

void Uart6_Init()
{
    GPIO_InitTypeDef  GPIO_InitStructure;
    USART_InitTypeDef USART_InitStructure;
    NVIC_InitTypeDef  NVIC_InitStructure;


    RCC_APB1PeriphClockCmd(RCC_APB1Periph_UART6 , ENABLE);
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOC , ENABLE);

    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0|GPIO_Pin_1;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;

    GPIO_Init(GPIOC, &GPIO_InitStructure);

    USART_InitStructure.USART_BaudRate = 115200;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStructure.USART_Mode = USART_Mode_Tx|USART_Mode_Rx;

    USART_Init(UART6, &USART_InitStructure);

    USART_ITConfig(UART6, USART_IT_RXNE,ENABLE);


    NVIC_InitStructure.NVIC_IRQChannel = UART6_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);


    USART_Cmd(UART6, ENABLE);
    USART_ClearFlag(UART6,USART_FLAG_TC);//??????6?????????
}

void Uart8_Init()
{
    GPIO_InitTypeDef  GPIO_InitStructure;
    USART_InitTypeDef USART_InitStructure;
    NVIC_InitTypeDef  NVIC_InitStructure;

    RCC_APB1PeriphClockCmd(RCC_APB1Periph_UART8 , ENABLE);
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOC , ENABLE);

    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_4|GPIO_Pin_5;//4TX 5RX
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;

    GPIO_Init(GPIOC, &GPIO_InitStructure);

    USART_InitStructure.USART_BaudRate = 115200;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStructure.USART_Mode = USART_Mode_Tx|USART_Mode_Rx;

    USART_Init(UART8, &USART_InitStructure);

    USART_ITConfig(UART8, USART_IT_RXNE,ENABLE);

    NVIC_InitStructure.NVIC_IRQChannel = UART8_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);

    USART_Cmd(UART8, ENABLE);
    USART_ClearFlag(UART8, USART_FLAG_TC); // ????????????
}

void uart6_send_string(u8* string,u8 len)
{
    u8 i;
    for(i=0;i<len;i++)
    {
        USART_SendData(UART6,string[i]);
        while(  USART_GetFlagStatus(UART6, USART_FLAG_TC)==0 );
    }
}
void uart8_send_string(u8* string, u8 len)
{
    u8 i;
    for(i = 0; i < len; i++)
    {
        USART_SendData(UART8, string[i]);
        while(USART_GetFlagStatus(UART8, USART_FLAG_TC) == 0); // ??????????
    }
}

//void UART7_DMA_Init(uint32_t baudrate)
//{
//    GPIO_InitTypeDef  GPIO_InitStructure;
//    USART_InitTypeDef USART_InitStructure;
//    NVIC_InitTypeDef  NVIC_InitStructure;
//    DMA_InitTypeDef DMA_InitStructure;
//
//
//    RCC_APB1PeriphClockCmd(RCC_APB1Periph_UART7 , ENABLE);
//    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOC , ENABLE);
//    RCC_AHBPeriphClockCmd( RCC_AHBPeriph_DMA1, ENABLE);
//
//    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_2|GPIO_Pin_3;//2TX 3RX
//    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
//    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
//    GPIO_Init(GPIOC, &GPIO_InitStructure);
//
//    //Uart7???????DMA
//    DMA_DeInit(DMA1_Channel6);                                         //???????
//    DMA_InitStructure.DMA_PeripheralBaseAddr = (u32)(&UART7->DATAR);        //??????????
//    DMA_InitStructure.DMA_MemoryBaseAddr = (u32)uart_dma_rx_buff.rec_buff; //???????????
//    DMA_InitStructure.DMA_DIR = DMA_DIR_PeripheralSRC;                      //???????
//    DMA_InitStructure.DMA_BufferSize = RX_BUFF_LENGTH;                      //???????
//    DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;        //????????????
//    DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;                 //???????????
//    DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_Byte; //???????????
//    DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_Byte;         //????????????
//    DMA_InitStructure.DMA_Mode = DMA_Mode_Normal;                           //???????
//    DMA_InitStructure.DMA_Priority = DMA_Priority_VeryHigh;                 //?????
//    DMA_InitStructure.DMA_M2M = DMA_M2M_Disable;                            //????M2M
//    DMA_Init(DMA1_Channel6, &DMA_InitStructure );
//
//
//    //Uart7???????DMA
//    DMA_DeInit(DMA1_Channel7);                                         //???????
//    DMA_InitStructure.DMA_PeripheralBaseAddr = (u32)(&UART7->DATAR);        //??????????
//    DMA_InitStructure.DMA_MemoryBaseAddr = (u32)uart_dma_rx_buff.rec_buff; //???????????
//    DMA_InitStructure.DMA_DIR = DMA_DIR_PeripheralDST;                      //????????
//    DMA_InitStructure.DMA_BufferSize = TX_BUFF_LENGTH;                      //???????
//    DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;        //????????????
//    DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;                 //???????????
//    DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_Byte; //???????????
//    DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_Byte;         //????????????
//    DMA_InitStructure.DMA_Mode = DMA_Mode_Normal;                           //???????
//    DMA_InitStructure.DMA_Priority = DMA_Priority_VeryHigh;                 //?????
//    DMA_InitStructure.DMA_M2M = DMA_M2M_Disable;                            //????M2M
//    DMA_Init(DMA1_Channel7, &DMA_InitStructure );
//
//
//
//    USART_InitStructure.USART_BaudRate = baudrate;
//    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
//    USART_InitStructure.USART_StopBits = USART_StopBits_1;
//    USART_InitStructure.USART_Parity = USART_Parity_No;
//    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
//    USART_InitStructure.USART_Mode = USART_Mode_Tx|USART_Mode_Rx;
//    USART_Init(UART7, &USART_InitStructure);
//
//    USART_ITConfig(UART7, USART_IT_IDLE, ENABLE);    //????????????????
//    USART_DMACmd(UART7, USART_DMAReq_Rx | USART_DMAReq_Tx, ENABLE);    //??USART2 DMA
//    USART_Cmd(UART7, ENABLE);
//
//    NVIC_InitStructure.NVIC_IRQChannel = UART7_IRQn;
//    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
//    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
//    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
//    NVIC_Init(&NVIC_InitStructure);
//
//    DMA_Cmd(DMA1_Channel6, ENABLE);
//}
//


void UART_SendData(uint8_t *data, uint16_t length)
{
    while(DMA_GetCurrDataCounter(DMA1_Channel7)); // ?????????????

    memcpy(uart_dma_rx_buff.send_buff, data, length);

    DMA_Cmd(DMA1_Channel7, DISABLE);
    DMA_SetCurrDataCounter(DMA1_Channel7, length);
    DMA_Cmd(DMA1_Channel7, ENABLE);

    while(!DMA_GetFlagStatus(DMA1_FLAG_TC7));
    DMA_ClearFlag(DMA1_FLAG_TC7);
}

// ??????????????????
//void USART1_IRQHandler(void) {
//    if (USART_GetITStatus(USART1, USART_IT_IDLE) != RESET) {
//        uint16_t temp;
//        // ?????????????
//        temp = USART1->SR;
//        temp = USART1->DR;
//
//        // ???????????????
//        uint16_t count = BUFFER_SIZE - DMA_GetCurrDataCounter(DMA1_Channel5);
//        for (uint16_t i = 0; i < count; i++) {
//            RingBuffer_Put(&rx_buffer, rx_buffer.buffer[i]);
//        }
//
//        // ???????? DMA ??????????
//        DMA_Cmd(DMA1_Channel5, DISABLE);
//        DMA_SetCurrDataCounter(DMA1_Channel5, BUFFER_SIZE);
//        DMA_Cmd(DMA1_Channel5, ENABLE);
//    }
//}

void usart1_send_string(u8* string,u8 len)
{
    u8 i;
    for(i=0;i<len;i++)
    {
        USART_SendData(USART1,string[i]);
        while(  USART_GetFlagStatus(USART1, USART_FLAG_TC)==0 );
    }
}




void Usart3_Init()
{
    GPIO_InitTypeDef  GPIO_InitStructure;
    USART_InitTypeDef USART_InitStructure;
    NVIC_InitTypeDef  NVIC_InitStructure;

    RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART3 , ENABLE);
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB , ENABLE);

    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_10|GPIO_Pin_11;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;

    GPIO_Init(GPIOB, &GPIO_InitStructure);
    USART_InitStructure.USART_BaudRate = 9600;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStructure.USART_Mode = USART_Mode_Tx|USART_Mode_Rx;
    USART_Init(USART3, &USART_InitStructure);
    USART_ITConfig(USART3, USART_IT_RXNE, ENABLE);
     NVIC_InitStructure.NVIC_IRQChannel = USART3_IRQn;
     NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
     NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
     NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
     NVIC_Init(&NVIC_InitStructure);

     USART_Cmd(USART3, ENABLE);
     USART_ClearFlag(USART3,USART_FLAG_TC);//??????3?????????
}




void Usart2_Init()
{
    GPIO_InitTypeDef  GPIO_InitStructure;
    USART_InitTypeDef USART_InitStructure;
    NVIC_InitTypeDef  NVIC_InitStructure;

    RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART2 , ENABLE);
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA , ENABLE);

    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_2|GPIO_Pin_3;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;

    GPIO_Init(GPIOA, &GPIO_InitStructure);
    USART_InitStructure.USART_BaudRate = 115200;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStructure.USART_Mode = USART_Mode_Tx|USART_Mode_Rx;
    USART_Init(USART2, &USART_InitStructure);
    USART_ITConfig(USART2, USART_IT_RXNE, ENABLE);
     NVIC_InitStructure.NVIC_IRQChannel = USART2_IRQn;
     NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
     NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
     NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
     NVIC_Init(&NVIC_InitStructure);

     USART_Cmd(USART2, ENABLE);
     USART_ClearFlag(USART2,USART_FLAG_TC);//??????2?????????
}
