#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K230 Camera Control Program
Receive UART commands to take photos and save to current directory
"""

import time
import os
from machine import UART
import image
try:
    from media.camera import *
    from media.display import *
    from media.media import *
    API_TYPE = "media"
except ImportError:
    try:
        import sensor
        import lcd
        API_TYPE = "canmv"
    except ImportError:
        API_TYPE = "none"
        print("Warning: No camera API available")
class K230Camera:
    def __init__(self):
        # UART config - corresponds to CH32V307 UART6 PC0/PC1
        self.uart = UART(1, baudrate=115200, bits=8, parity=None, stop=1, timeout=1000, read_buf_len=4096)

        # Photo counter
        self.photo_count = 0

        # Initialize camera system
        self.init_camera()
        
    def init_camera(self):
        """Initialize camera system"""
        global API_TYPE
        try:
            if API_TYPE == "media":
                # Media API initialization
                media.initialize()
                camera.sensor_init(CAM_DEV_ID_0, CAM_DEFAULT_SENSOR)
                camera.set_outsize(CAM_DEV_ID_0, CAM_CHN_ID_0, 320, 240)
                camera.set_outfmt(CAM_DEV_ID_0, CAM_CHN_ID_0, PIXEL_FORMAT_RGB_565)
                display.init(DISPLAY_TYPE_ST7701, width=800, height=480, to_ide=True)
                display.set_plane(0, 0, 800, 480, PIXEL_FORMAT_RGB_565, DISPLAY_MIRROR_NONE, DISPLAY_CHN_VIDEO1)
                camera.start_stream(CAM_DEV_ID_0)

            elif API_TYPE == "canmv":
                # CanMV API initialization
                lcd.init()
                lcd.clear()
                sensor.reset()
                sensor.set_pixformat(sensor.RGB565)
                sensor.set_framesize(sensor.QVGA)
                sensor.skip_frames(time=2000)

            else:
                # No API available - simulation mode
                print("Running in simulation mode")

            print(f"K230 camera initialized successfully (API: {API_TYPE})")
            self.send_response("CAMERA_INIT_OK")

        except Exception as e:
            print(f"Camera initialization failed: {e}")
            self.send_response("CAMERA_INIT_FAIL")
    
    def send_response(self, msg):
        """发送响应消息到UART"""
        try:
            response = f"{msg}\r\n"
            self.uart.write(response.encode())
            print(f"发送响应: {msg}")
        except Exception as e:
            print(f"发送响应失败: {e}")
    
    def capture_photo(self):
        """Take photo and save"""
        global API_TYPE
        try:
            # Get current timestamp
            timestamp = time.ticks_ms()
            filename = f"photo_{self.photo_count:04d}_{timestamp}.jpg"

            # Capture image based on API
            img = None
            if API_TYPE == "media":
                img = camera.capture_image(CAM_DEV_ID_0, CAM_CHN_ID_0)
            elif API_TYPE == "canmv":
                img = sensor.snapshot()
            else:
                # Simulation mode
                with open(filename.replace('.jpg', '.txt'), 'w') as f:
                    f.write(f"Simulation photo {self.photo_count} at {timestamp}")
                self.photo_count += 1
                print(f"Simulation photo saved: {filename}")
                self.send_response(f"PHOTO_OK:{filename}")
                return True

            if img is None:
                print("Image capture failed")
                self.send_response("PHOTO_CAPTURE_FAIL")
                return False

            # Save image to current directory
            try:
                if hasattr(img, 'save'):
                    img.save(filename)
                else:
                    # Alternative save method for media API
                    with open(filename, 'wb') as f:
                        f.write(bytes(img))

                self.photo_count += 1
                print(f"Photo taken successfully, saved as: {filename}")
                self.send_response(f"PHOTO_OK:{filename}")

                # Display on LCD if available
                if API_TYPE == "canmv":
                    try:
                        lcd.display(img)
                    except:
                        pass
                elif API_TYPE == "media":
                    try:
                        display.show_image(img, 0, 0, DISPLAY_CHN_VIDEO1)
                    except:
                        pass

                return True

            except Exception as e:
                print(f"Image save failed: {e}")
                self.send_response("PHOTO_SAVE_FAIL")
                return False

        except Exception as e:
            print(f"Photo process error: {e}")
            self.send_response("PHOTO_ERROR")
            return False
    
    def process_command(self, cmd):
        """Process received command"""
        cmd = cmd.strip().lower()
        print(f"Received command: {cmd}")

        if cmd == "photo":
            print("Executing photo command")
            self.capture_photo()
        elif cmd == "status":
            self.send_response(f"STATUS_OK:COUNT_{self.photo_count}")
        elif cmd == "test":
            self.send_response("TEST_OK")
        elif cmd == "reset":
            self.photo_count = 0
            self.send_response("RESET_OK")
        else:
            print(f"Unknown command: {cmd}")
            self.send_response(f"UNKNOWN_CMD:{cmd}")
    
    def run(self):
        """Main running loop"""
        print("K230 camera program started")
        self.send_response("K230_READY")

        rx_buffer = ""

        while True:
            try:
                # Check UART reception
                if self.uart.any():
                    data = self.uart.read()
                    if data:
                        try:
                            rx_buffer += data.decode('utf-8')
                        except:
                            rx_buffer += data.decode('utf-8', errors='ignore')

                        # Process complete command lines (ending with \r\n)
                        while '\r\n' in rx_buffer or '\n' in rx_buffer:
                            if '\r\n' in rx_buffer:
                                line, rx_buffer = rx_buffer.split('\r\n', 1)
                            else:
                                line, rx_buffer = rx_buffer.split('\n', 1)

                            if line.strip():
                                self.process_command(line.strip())

                # Display preview (optional)
                try:
                    if API_TYPE == "canmv":
                        img = sensor.snapshot()
                        if img:
                            lcd.display(img)
                    elif API_TYPE == "media":
                        # Skip preview for media API to avoid errors
                        pass
                except:
                    pass  # Preview failure does not affect main function

                time.sleep_ms(50)  # Short delay

            except KeyboardInterrupt:
                print("Program exit")
                break
            except Exception as e:
                print(f"Runtime error: {e}")
                time.sleep_ms(100)
    
    def cleanup(self):
        """Clean up resources"""
        try:
            # Simple cleanup for CanMV
            print("Resource cleanup completed")
        except Exception as e:
            print(f"Error during resource cleanup: {e}")

def main():
    """Main function"""
    print("=== K230 Camera Control Program ===")
    print("Supported commands:")
    print("  photo  - Take photo and save")
    print("  status - Query status")
    print("  test   - Test connection")
    print("  reset  - Reset photo counter")
    print("===================================")

    camera_app = None
    try:
        camera_app = K230Camera()
        camera_app.run()
    except Exception as e:
        print(f"Program exception: {e}")
    finally:
        if camera_app:
            camera_app.cleanup()
        print("Program ended")

if __name__ == "__main__":
    main()
