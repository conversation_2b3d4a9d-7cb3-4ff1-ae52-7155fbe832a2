#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
庐山派K230相机控制程序
接收UART命令进行拍照并保存到当前目录
"""

import time
import os
from machine import UART
from media.camera import *
from media.display import *
from media.media import *

class K230Camera:
    def __init__(self):
        # UART配置 - 对应CH32V307的UART6 PC0/PC1
        self.uart = UART(UART.UART0, baudrate=115200, bits=8, parity=None, stop=1)
        
        # 相机配置
        self.camera_width = 1920
        self.camera_height = 1080
        self.display_width = 800
        self.display_height = 480
        
        # 拍照计数器
        self.photo_count = 0
        
        # 初始化媒体系统
        self.init_media()
        
    def init_media(self):
        """初始化媒体系统"""
        try:
            # 初始化媒体管理器
            media.initialize()
            
            # 初始化相机
            camera.sensor_init(CAM_DEV_ID_0, CAM_DEFAULT_SENSOR)
            
            # 设置相机输出格式
            camera.set_outsize(CAM_DEV_ID_0, CAM_CHN_ID_0, self.camera_width, self.camera_height)
            camera.set_outfmt(CAM_DEV_ID_0, CAM_CHN_ID_0, PIXEL_FORMAT_RGB_888_PLANAR)
            
            # 初始化显示器
            display.init(DISPLAY_TYPE_ST7701, width=self.display_width, height=self.display_height, to_ide=True)
            display.set_plane(0, 0, self.display_width, self.display_height, PIXEL_FORMAT_RGB_565, DISPLAY_MIRROR_NONE, DISPLAY_CHN_VIDEO1)
            
            # 启动相机
            camera.start_stream(CAM_DEV_ID_0)
            
            print("K230相机初始化成功")
            self.send_response("CAMERA_INIT_OK")
            
        except Exception as e:
            print(f"相机初始化失败: {e}")
            self.send_response("CAMERA_INIT_FAIL")
    
    def send_response(self, msg):
        """发送响应消息到UART"""
        try:
            response = f"{msg}\r\n"
            self.uart.write(response.encode())
            print(f"发送响应: {msg}")
        except Exception as e:
            print(f"发送响应失败: {e}")
    
    def capture_photo(self):
        """拍照并保存"""
        try:
            # 获取当前时间戳
            timestamp = time.ticks_ms()
            filename = f"photo_{self.photo_count:04d}_{timestamp}.jpg"
            
            # 捕获图像
            img = camera.capture_image(CAM_DEV_ID_0, CAM_CHN_ID_0)
            if img is None:
                print("捕获图像失败")
                self.send_response("PHOTO_CAPTURE_FAIL")
                return False
            
            # 保存图像到当前目录
            try:
                # 将图像数据保存为JPEG文件
                with open(filename, 'wb') as f:
                    # 这里需要将图像数据转换为JPEG格式
                    # K230的具体实现可能需要调用相应的编码函数
                    jpeg_data = self.convert_to_jpeg(img)
                    f.write(jpeg_data)
                
                self.photo_count += 1
                print(f"拍照成功，保存为: {filename}")
                self.send_response(f"PHOTO_OK:{filename}")
                return True
                
            except Exception as e:
                print(f"保存图像失败: {e}")
                self.send_response("PHOTO_SAVE_FAIL")
                return False
                
        except Exception as e:
            print(f"拍照过程出错: {e}")
            self.send_response("PHOTO_ERROR")
            return False
    
    def convert_to_jpeg(self, img):
        """将图像转换为JPEG格式"""
        # 这里是简化的实现，实际需要根据K230的API进行JPEG编码
        # 可能需要使用image.save()或其他编码函数
        try:
            # 假设img对象有save方法或可以直接获取JPEG数据
            if hasattr(img, 'to_jpeg'):
                return img.to_jpeg()
            elif hasattr(img, 'save'):
                # 临时保存然后读取
                temp_file = "temp.jpg"
                img.save(temp_file)
                with open(temp_file, 'rb') as f:
                    data = f.read()
                os.remove(temp_file)
                return data
            else:
                # 如果没有直接的JPEG转换方法，返回原始数据
                # 这需要根据实际的K230 API进行调整
                return bytes(img)
        except Exception as e:
            print(f"JPEG转换失败: {e}")
            raise e
    
    def process_command(self, cmd):
        """处理接收到的命令"""
        cmd = cmd.strip().lower()
        print(f"收到命令: {cmd}")
        
        if cmd == "photo":
            print("执行拍照命令")
            self.capture_photo()
        elif cmd == "status":
            self.send_response(f"STATUS_OK:COUNT_{self.photo_count}")
        elif cmd == "test":
            self.send_response("TEST_OK")
        else:
            print(f"未知命令: {cmd}")
            self.send_response(f"UNKNOWN_CMD:{cmd}")
    
    def run(self):
        """主运行循环"""
        print("K230相机程序启动")
        self.send_response("K230_READY")
        
        rx_buffer = ""
        
        while True:
            try:
                # 检查UART接收
                if self.uart.any():
                    data = self.uart.read().decode('utf-8', errors='ignore')
                    rx_buffer += data
                    
                    # 处理完整的命令行（以\r\n结尾）
                    while '\r\n' in rx_buffer or '\n' in rx_buffer:
                        if '\r\n' in rx_buffer:
                            line, rx_buffer = rx_buffer.split('\r\n', 1)
                        else:
                            line, rx_buffer = rx_buffer.split('\n', 1)
                        
                        if line.strip():
                            self.process_command(line.strip())
                
                # 显示预览（可选）
                try:
                    img = camera.capture_image(CAM_DEV_ID_0, CAM_CHN_ID_0)
                    if img:
                        display.show_image(img, 0, 0, DISPLAY_CHN_VIDEO1)
                except:
                    pass  # 预览失败不影响主功能
                
                time.sleep_ms(10)  # 短暂延时
                
            except KeyboardInterrupt:
                print("程序退出")
                break
            except Exception as e:
                print(f"运行时错误: {e}")
                time.sleep_ms(100)
    
    def cleanup(self):
        """清理资源"""
        try:
            camera.stop_stream(CAM_DEV_ID_0)
            display.deinit()
            media.deinitialize()
            print("资源清理完成")
        except Exception as e:
            print(f"清理资源时出错: {e}")

def main():
    """主函数"""
    camera_app = None
    try:
        camera_app = K230Camera()
        camera_app.run()
    except Exception as e:
        print(f"程序异常: {e}")
    finally:
        if camera_app:
            camera_app.cleanup()

if __name__ == "__main__":
    main()
